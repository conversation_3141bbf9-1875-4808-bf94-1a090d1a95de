
import React, { useState, useRef, useEffect } from 'react';
import { useAIAssistant } from '../../hooks/useAIAssistant';
import Modal from '../ui/Modal';
import { Page } from '../../types';
import { isAIAvailable } from '../../services/geminiService';

interface AIAssistantProps {
  isOpen: boolean;
  onClose: () => void;
  workspaceId: string;
  onNavigate: (page: Page, context?: any) => void;
  onOpenTransactionModal: (prompt: string) => void;
}

// --- SVG Icons for UI ---
const SparkleIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 20 20" fill="currentColor">
    <path fillRule="evenodd" d="M10 3a.75.75 0 01.75.75v1.5a.75.75 0 01-1.5 0v-1.5A.75.75 0 0110 3zM10 14.75a.75.75 0 01.75.75v1.5a.75.75 0 01-1.5 0v-1.5a.75.75 0 01.75-.75zM5.036 5.036a.75.75 0 011.06 0l1.061 1.06a.75.75 0 01-1.06 1.06l-1.06-1.06a.75.75 0 010-1.06zm9.928 9.928a.75.75 0 011.06 0l1.061 1.06a.75.75 0 01-1.06 1.06l-1.06-1.06a.75.75 0 010-1.06zM3 10a.75.75 0 01.75-.75h1.5a.75.75 0 010 1.5h-1.5A.75.75 0 013 10zm13.25 0a.75.75 0 01.75-.75h1.5a.75.75 0 010 1.5h-1.5a.75.75 0 01-.75-.75zM5.036 14.964a.75.75 0 010-1.06l1.06-1.06a.75.75 0 011.06 1.06l-1.06 1.06a.75.75 0 01-1.06 0zm9.928-9.928a.75.75 0 010-1.06l1.06-1.06a.75.75 0 011.06 1.06l-1.06 1.06a.75.75 0 01-1.06 0z" clipRule="evenodd" />
  </svg>
);
const UserIcon: React.FC<{ className?: string }> = ({ className }) => (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" /></svg>
);
const SendIcon: React.FC<{ className?: string }> = ({ className }) => (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} fill="currentColor" viewBox="0 0 20 20"><path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.428A1 1 0 009.894 16L4.894 4.571l7-14z" /></svg>
);
const SpinnerIcon: React.FC<{ className?: string }> = ({ className }) => (
    <svg className={className} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
);

const AIAvatar = () => (
    <div className="w-8 h-8 rounded-full bg-gray-800 flex items-center justify-center flex-shrink-0">
        <SparkleIcon className="h-5 w-5 text-white" />
    </div>
);

const UserAvatar = () => (
    <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center flex-shrink-0">
        <UserIcon className="h-5 w-5 text-gray-500" />
    </div>
);

const LoadingBubble = () => (
    <div className="flex items-start gap-3">
        <AIAvatar />
        <div className="bg-gray-100 rounded-lg p-3">
            <div className="flex items-center justify-center space-x-1">
                <span className="h-1.5 w-1.5 bg-gray-400 rounded-full animate-pulse [animation-delay:-0.3s]"></span>
                <span className="h-1.5 w-1.5 bg-gray-400 rounded-full animate-pulse [animation-delay:-0.15s]"></span>
                <span className="h-1.5 w-1.5 bg-gray-400 rounded-full animate-pulse"></span>
            </div>
        </div>
    </div>
);

const SuggestionChip: React.FC<{ text: string, onClick: () => void, disabled: boolean }> = ({ text, onClick, disabled }) => (
    <button
        type="button"
        onClick={onClick}
        className="bg-black text-white px-3 py-1.5 rounded-lg text-sm hover:bg-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        disabled={disabled}
    >
        {text}
    </button>
);

const AIAssistant: React.FC<AIAssistantProps> = ({ isOpen, onClose, workspaceId, onNavigate, onOpenTransactionModal }) => {
  const { messages, isLoading, sendMessage } = useAIAssistant(workspaceId, onNavigate, onOpenTransactionModal);
  const [inputText, setInputText] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(scrollToBottom, [messages, isLoading]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (isLoading || !inputText.trim() || !isAIAvailable) return;
    sendMessage(inputText);
    setInputText('');
  };

  const handleSuggestionClick = (suggestion: string) => {
    if (isLoading || !isAIAvailable) return;
    sendMessage(suggestion);
  };
  
  const welcomeSuggestions = [
    "What are my total unpaid invoices?",
    "Record paying rent of $1200",
    "Show me my reports",
  ];

  const disabledPlaceholder = !isAIAvailable ? "AI is unavailable. API key not configured." : "Ask a question or record a transaction...";

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="AI Assistant" size="lg">
      <div className="flex flex-col h-[80vh] bg-white">
        <div className="flex-1 p-6 overflow-y-auto">
          <div className="space-y-6">
            {messages.length === 0 && (
               <div className="text-center text-gray-500 pt-10">
                  <div className="inline-block p-3 bg-gray-800 rounded-full mb-4">
                    <SparkleIcon className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900">Hello! I'm FinHog AI.</h3>
                  <p className="text-sm mt-1">{isAIAvailable ? "How can I help you today?" : "AI features are currently unavailable."}</p>
                  <div className="flex flex-wrap gap-2 justify-center mt-6">
                      {welcomeSuggestions.map(prompt => (
                          <SuggestionChip key={prompt} text={prompt} onClick={() => handleSuggestionClick(prompt)} disabled={!isAIAvailable} />
                      ))}
                  </div>
               </div>
            )}
            {messages.map((msg, index) => (
              <div key={index} className={`flex items-start gap-3 ${msg.role === 'user' ? 'justify-end' : ''}`}>
                {msg.role === 'model' && <AIAvatar />}
                <div className={`max-w-md lg:max-w-lg rounded-lg p-3 text-sm ${
                    msg.role === 'user'
                      ? 'bg-gray-900 text-white'
                      : 'bg-gray-100 text-gray-900'
                  }`}
                >
                  <p className="whitespace-pre-wrap">{msg.text}</p>
                </div>
                {msg.role === 'user' && <UserAvatar />}
              </div>
            ))}
            {isLoading && <LoadingBubble />}
            <div ref={messagesEndRef} />
          </div>
        </div>
        <div className="p-4 border-t border-gray-200 bg-white">
          <form onSubmit={handleSubmit} className="flex items-center space-x-3">
            <input
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              placeholder={disabledPlaceholder}
              className="flex-1 block w-full px-4 py-2 bg-gray-100 border border-transparent rounded-full focus:outline-none focus:ring-2 focus:ring-gray-800 focus:border-transparent text-sm disabled:cursor-not-allowed"
              disabled={isLoading || !isAIAvailable}
            />
            <button type="submit" disabled={isLoading || !inputText.trim() || !isAIAvailable} className="w-9 h-9 flex-shrink-0 rounded-full bg-black text-white flex items-center justify-center hover:bg-gray-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black">
              {isLoading ? <SpinnerIcon className="animate-spin h-5 w-5" /> : <SendIcon className="h-4 w-4" />}
            </button>
          </form>
        </div>
      </div>
    </Modal>
  );
};

export default AIAssistant;
