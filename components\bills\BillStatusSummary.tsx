import React, { useMemo } from 'react';
import { Bill, BillStatus } from '../../types';
import Card from '../ui/Card';

interface StatCardProps {
  label: string;
  value: string;
  isLoading: boolean;
}

const StatCard: React.FC<StatCardProps> = ({ label, value, isLoading }) => (
  <Card padding="sm">
    <dt className="text-sm font-medium text-gray-500 truncate">{label}</dt>
    {isLoading ? (
        <dd className="mt-1 h-8 w-28 bg-gray-200 rounded animate-pulse"></dd>
    ) : (
        <dd className="mt-1 text-3xl font-semibold text-gray-900">{value}</dd>
    )}
  </Card>
);

interface BillStatusSummaryProps {
  bills: Bill[] | undefined;
  isLoading: boolean;
}

const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);
};

const BillStatusSummary: React.FC<BillStatusSummaryProps> = ({ bills, isLoading }) => {
    const stats = useMemo(() => {
        // Provide default empty array if bills is undefined
        const safeBills = bills || [];

        const totalUnpaid = safeBills
            .filter(b => b.status === BillStatus.Unpaid || b.status === BillStatus.Overdue)
            .reduce((sum, b) => sum + b.amount, 0);
        const overdue = safeBills
            .filter(b => b.status === BillStatus.Overdue)
            .reduce((sum, b) => sum + b.amount, 0);
        const paidThisMonth = safeBills
            .filter(b => b.status === BillStatus.Paid) // In real app, filter by current month
            .reduce((sum, b) => sum + b.amount, 0);

        return { totalUnpaid, overdue, paidThisMonth };
    }, [bills]);

    return (
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-3">
            <StatCard label="Total Owed" value={formatCurrency(stats.totalUnpaid)} isLoading={isLoading} />
            <StatCard label="Overdue" value={formatCurrency(stats.overdue)} isLoading={isLoading} />
            <StatCard label="Paid (All Time)" value={formatCurrency(stats.paidThisMonth)} isLoading={isLoading} />
        </div>
    );
};

export default BillStatusSummary;