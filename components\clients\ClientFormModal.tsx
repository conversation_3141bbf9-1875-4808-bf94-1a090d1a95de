import React, { useState, useEffect, useMemo } from 'react';
import { Client } from '../../types';
import Modal from '../ui/Modal';
import Input from '../ui/Input';
import Button from '../ui/Button';

interface ClientFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (client: Omit<Client, '_id' | '_creationTime' | 'workspaceId'>) => void;
  client: Client | null;
}

const newClientTemplate: Omit<Client, '_id' | '_creationTime' | 'workspaceId'> = {
  name: '',
  email: '',
  phone: '',
  address: '',
};

const ClientFormModal: React.FC<ClientFormModalProps> = ({ isOpen, onClose, onSave, client }) => {
  const [formData, setFormData] = useState<Omit<Client, '_id' | '_creationTime' | 'workspaceId'>>({ ...newClientTemplate });
  const isEditing = useMemo(() => !!client, [client]);

  useEffect(() => {
    if (isOpen) {
      if (client) {
        const { _id, _creationTime, workspaceId, ...editableData } = client;
        setFormData(editableData);
      } else {
        setFormData({ ...newClientTemplate });
      }
    }
  }, [client, isOpen]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };
  
  return (
    <Modal isOpen={isOpen} onClose={onClose} title={isEditing ? 'Edit Client' : 'New Client'} size="lg">
      <form onSubmit={handleSubmit}>
        <div className="p-6 space-y-6">
          <Input label="Client Name" name="name" value={formData.name} onChange={handleChange} required />
           <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Input label="Email" name="email" type="email" value={formData.email || ''} onChange={handleChange} />
            <Input label="Phone" name="phone" value={formData.phone || ''} onChange={handleChange} />
          </div>
          <div>
            <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">Address</label>
            <textarea
                id="address"
                name="address"
                rows={3}
                className="block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-gray-900 focus:border-gray-900 sm:text-sm"
                value={formData.address || ''}
                onChange={handleChange}
            />
          </div>
        </div>
        <div className="px-6 py-4 bg-gray-50 border-t flex justify-end space-x-2">
          <Button type="button" variant="secondary" onClick={onClose}>Cancel</Button>
          <Button type="submit">{isEditing ? 'Save Changes' : 'Create Client'}</Button>
        </div>
      </form>
    </Modal>
  );
};

export default ClientFormModal;