import React, { useMemo } from 'react';
import { Client, Invoice, Quote, JournalEntry } from '../../types';
import Modal from '../ui/Modal';
import Button from '../ui/Button';
import Card from '../ui/Card';

interface ClientStatementModalProps {
  isOpen: boolean;
  onClose: () => void;
  client: Client | null;
  invoices: Invoice[];
  quotes: Quote[];
  journalEntries: JournalEntry[];
}

const formatCurrency = (amount: number) => 
  new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);

const formatDate = (date: string | number) => 
  new Date(date).toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'short', 
    day: 'numeric' 
  });

interface StatementItem {
  date: string;
  type: 'Invoice' | 'Quote' | 'Payment' | 'Transaction';
  reference: string;
  description: string;
  amount: number;
  status?: string;
}

const ClientStatementModal: React.FC<ClientStatementModalProps> = ({
  isOpen,
  onClose,
  client,
  invoices,
  quotes,
  journalEntries
}) => {
  const statementItems = useMemo(() => {
    if (!client) return [];

    const items: StatementItem[] = [];

    // Add invoices
    invoices
      .filter(invoice => invoice.clientId === client._id)
      .forEach(invoice => {
        items.push({
          date: invoice.issueDate,
          type: 'Invoice',
          reference: invoice.invoiceNumber || `INV-${invoice._id.slice(-6).toUpperCase()}`,
          description: `Invoice for services`,
          amount: invoice.amount,
          status: invoice.status
        });
      });

    // Add quotes
    quotes
      .filter(quote => quote.clientId === client._id)
      .forEach(quote => {
        items.push({
          date: quote.issueDate,
          type: 'Quote',
          reference: `QUO-${quote._id.slice(-6).toUpperCase()}`,
          description: `Quote for services`,
          amount: quote.amount,
          status: quote.status
        });
      });

    // Add relevant journal entries (payments and transactions related to the client)
    journalEntries
      .filter(entry => 
        entry.description.toLowerCase().includes(client.name.toLowerCase()) ||
        entry.debits.some(d => d.accountName.toLowerCase().includes('receivable')) ||
        entry.credits.some(c => c.accountName.toLowerCase().includes('receivable'))
      )
      .forEach(entry => {
        // Check if this is a payment (credit to accounts receivable)
        const receivableCredit = entry.credits.find(c => 
          c.accountName.toLowerCase().includes('receivable')
        );
        
        if (receivableCredit) {
          items.push({
            date: entry.date,
            type: 'Payment',
            reference: `PAY-${entry._id.slice(-6).toUpperCase()}`,
            description: entry.description,
            amount: -receivableCredit.amount, // Negative because it reduces the balance
            status: 'Received'
          });
        } else {
          // Other transactions
          const totalAmount = entry.debits.reduce((sum, d) => sum + d.amount, 0);
          items.push({
            date: entry.date,
            type: 'Transaction',
            reference: `TXN-${entry._id.slice(-6).toUpperCase()}`,
            description: entry.description,
            amount: totalAmount,
            status: 'Recorded'
          });
        }
      });

    // Sort by date (newest first)
    return items.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  }, [client, invoices, quotes, journalEntries]);

  const summary = useMemo(() => {
    const totalInvoiced = statementItems
      .filter(item => item.type === 'Invoice')
      .reduce((sum, item) => sum + item.amount, 0);

    const totalPaid = statementItems
      .filter(item => item.type === 'Payment')
      .reduce((sum, item) => sum + Math.abs(item.amount), 0);

    const outstandingBalance = totalInvoiced - totalPaid;

    return {
      totalInvoiced,
      totalPaid,
      outstandingBalance
    };
  }, [statementItems]);

  if (!client) return null;

  const getStatusBadge = (type: string, status?: string) => {
    if (!status) return null;

    let colorClass = '';
    switch (status) {
      case 'Paid':
      case 'Accepted':
      case 'Received':
        colorClass = 'bg-green-100 text-green-800';
        break;
      case 'Sent':
      case 'Recorded':
        colorClass = 'bg-blue-100 text-blue-800';
        break;
      case 'Draft':
        colorClass = 'bg-gray-100 text-gray-800';
        break;
      case 'Overdue':
        colorClass = 'bg-red-100 text-red-800';
        break;
      case 'Declined':
        colorClass = 'bg-red-100 text-red-800';
        break;
      default:
        colorClass = 'bg-gray-100 text-gray-800';
    }

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClass}`}>
        {status}
      </span>
    );
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={`Client Statement - ${client.name}`} size="xl">
      <div className="p-6 space-y-6">
        {/* Client Information */}
        <Card>
          <div className="p-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Client Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-500">Name:</span>
                <span className="ml-2 text-gray-900">{client.name}</span>
              </div>
              {client.email && (
                <div>
                  <span className="font-medium text-gray-500">Email:</span>
                  <span className="ml-2 text-gray-900">{client.email}</span>
                </div>
              )}
              {client.phone && (
                <div>
                  <span className="font-medium text-gray-500">Phone:</span>
                  <span className="ml-2 text-gray-900">{client.phone}</span>
                </div>
              )}
              {client.address && (
                <div>
                  <span className="font-medium text-gray-500">Address:</span>
                  <span className="ml-2 text-gray-900">{client.address}</span>
                </div>
              )}
            </div>
          </div>
        </Card>

        {/* Account Summary */}
        <Card>
          <div className="p-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Account Summary</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{formatCurrency(summary.totalInvoiced)}</div>
                <div className="text-sm text-gray-500">Total Invoiced</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{formatCurrency(summary.totalPaid)}</div>
                <div className="text-sm text-gray-500">Total Paid</div>
              </div>
              <div className="text-center">
                <div className={`text-2xl font-bold ${summary.outstandingBalance > 0 ? 'text-red-600' : 'text-green-600'}`}>
                  {formatCurrency(summary.outstandingBalance)}
                </div>
                <div className="text-sm text-gray-500">Outstanding Balance</div>
              </div>
            </div>
          </div>
        </Card>

        {/* Transaction History */}
        <Card>
          <div className="p-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Transaction History</h3>
            {statementItems.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                No transactions found for this client.
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Type
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Reference
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Description
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Amount
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {statementItems.map((item, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {formatDate(item.date)}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {item.type}
                        </td>
                        <td className="px-4 py-3 text-sm font-mono text-gray-900">
                          {item.reference}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {item.description}
                        </td>
                        <td className="px-4 py-3 text-sm">
                          {getStatusBadge(item.type, item.status)}
                        </td>
                        <td className={`px-4 py-3 text-sm text-right font-mono ${
                          item.amount < 0 ? 'text-green-600' : 'text-gray-900'
                        }`}>
                          {item.amount < 0 ? '-' : ''}{formatCurrency(Math.abs(item.amount))}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </Card>
      </div>

      <div className="px-6 py-4 bg-gray-50 border-t flex justify-end space-x-3">
        <Button variant="secondary" onClick={onClose}>
          Close
        </Button>
        <Button variant="primary" onClick={() => window.print()}>
          Print Statement
        </Button>
      </div>
    </Modal>
  );
};

export default ClientStatementModal;
