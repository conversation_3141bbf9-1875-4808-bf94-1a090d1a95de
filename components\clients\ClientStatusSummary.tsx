import React, { useMemo } from 'react';
import { Client } from '../../types';
import Card from '../ui/Card';

interface StatCardProps {
  label: string;
  value: string | number;
  isLoading: boolean;
}

const StatCard: React.FC<StatCardProps> = ({ label, value, isLoading }) => (
  <Card padding="sm">
    <dt className="text-sm font-medium text-gray-500 truncate">{label}</dt>
    {isLoading ? (
        <dd className="mt-1 h-8 w-20 bg-gray-200 rounded animate-pulse"></dd>
    ) : (
        <dd className="mt-1 text-3xl font-semibold text-gray-900">{value}</dd>
    )}
  </Card>
);

interface ClientStatusSummaryProps {
  clients: Client[];
  isLoading: boolean;
}

const ClientStatusSummary: React.FC<ClientStatusSummaryProps> = ({ clients, isLoading }) => {
    const stats = useMemo(() => {
        if (!clients) {
            return {
                totalClients: 0,
                newThisMonth: 0,
                activeClients: 0
            };
        }

        const totalClients = clients.length;
        const newThisMonth = clients.filter(c => {
            const clientDate = new Date(c.createdAt || c._creationTime);
            const today = new Date();
            return clientDate.getMonth() === today.getMonth() && clientDate.getFullYear() === today.getFullYear();
        }).length;
        
        return { totalClients, newThisMonth };
    }, [clients]);

    return (
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
            <StatCard label="Total Clients" value={stats.totalClients} isLoading={isLoading} />
            <StatCard label="New This Month" value={stats.newThisMonth} isLoading={isLoading} />
        </div>
    );
};

export default ClientStatusSummary;
