import React, { useMemo } from 'react';
import { ResponsiveContainer, BarChart, CartesianGrid, XAxis, YAxis, Tooltip, Legend, Bar } from 'recharts';
import Card from '../ui/Card';
import { CashFlowData, JournalEntry, Account } from '../../types';
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";

interface CashFlowChartProps {
  workspaceId: string;
}

const processCashFlowData = (entries: JournalEntry[], accounts: Account[]): CashFlowData[] => {
    const cashAccountIds = new Set(accounts.filter(a => a.type === 'Asset' && (a.name.includes("Checking") || a.name.includes("Cash"))).map(a => a._id));
    const monthlyData: { [key: string]: { income: number, expenses: number } } = {};

    entries.forEach(entry => {
        const month = new Date(entry.date).toLocaleString('default', { month: 'short' });
        if (!monthlyData[month]) {
            monthlyData[month] = { income: 0, expenses: 0 };
        }
        entry.debits.forEach((d) => {
            if (cashAccountIds.has(d.accountId)) monthlyData[month].income += d.amount;
        });
        entry.credits.forEach((c) => {
            if (cashAccountIds.has(c.accountId)) monthlyData[month].expenses += c.amount;
        });
    });

    return Object.keys(monthlyData).map(month => ({
        month,
        income: monthlyData[month].income,
        expenses: monthlyData[month].expenses
    })).slice(-7); // Get last 7 months for display
};

const CashFlowChart: React.FC<CashFlowChartProps> = ({ workspaceId }) => {
  const entries = useQuery(api.journalEntries.getJournalEntries, { workspaceId: workspaceId as any });
  const accounts = useQuery(api.accounts.getAccounts, { workspaceId: workspaceId as any });
  const isLoading = entries === undefined || accounts === undefined;

  const data = useMemo(() => {
      if (isLoading || !entries || !accounts) return [];
      return processCashFlowData(entries, accounts);
  }, [isLoading, entries, accounts]);

  return (
    <Card className="h-96">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Cash Flow</h3>
        {isLoading ? (
            <div className="w-full h-full flex items-center justify-center">
                <div className="w-full h-64 bg-gray-200 rounded animate-pulse"></div>
            </div>
        ) : (
            <ResponsiveContainer width="100%" height="90%">
                <BarChart data={data} margin={{ top: 5, right: 20, left: -10, bottom: 5 }}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
                    <XAxis dataKey="month" tick={{ fill: '#6c757d', fontSize: 12 }} />
                    <YAxis tick={{ fill: '#6c757d', fontSize: 12 }} tickFormatter={(value) => `$${Number(value) / 1000}k`}/>
                    <Tooltip
                        contentStyle={{
                            backgroundColor: '#ffffff',
                            border: '1px solid #ced4da',
                            borderRadius: '0.25rem',
                        }}
                        labelStyle={{ color: '#15171a', fontWeight: 'bold' }}
                    />
                    <Legend wrapperStyle={{ fontSize: '14px' }} />
                    <Bar dataKey="income" fill="#212529" name="Income" radius={[4, 4, 0, 0]} />
                    <Bar dataKey="expenses" fill="#adb5bd" name="Expenses" radius={[4, 4, 0, 0]} />
                </BarChart>
            </ResponsiveContainer>
        )}
    </Card>
  );
};

export default CashFlowChart;