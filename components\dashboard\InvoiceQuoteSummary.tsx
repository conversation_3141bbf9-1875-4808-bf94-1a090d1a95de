import React, { useMemo } from 'react';
import Card from '../ui/Card';
import { Invoice, InvoiceStatus, Quote, QuoteStatus } from '../../types';
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";

interface InvoiceQuoteSummaryProps {
    workspaceId: string;
}

const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);
};

const MetricItem: React.FC<{ label: string, value: string | number, isLoading: boolean }> = ({ label, value, isLoading }) => (
    <div>
        <dt className="text-sm font-medium text-gray-500 truncate">{label}</dt>
        {isLoading ? (
            <dd className="mt-1 h-7 w-24 bg-gray-200 rounded animate-pulse"></dd>
        ) : (
            <dd className="mt-1 text-2xl font-semibold text-gray-900">{value}</dd>
        )}
    </div>
);

const InvoiceQuoteSummary: React.FC<InvoiceQuoteSummaryProps> = ({ workspaceId }) => {
    const invoices = useQuery(api.invoices.getInvoices, { workspaceId: workspaceId as any });
    const quotes = useQuery(api.quotes.getQuotes, { workspaceId: workspaceId as any });
    const isLoading = invoices === undefined || quotes === undefined;

    const { invoiceMetrics, quoteMetrics } = useMemo(() => {
        const defaultMetrics = {
            outstanding: 0, overdue: 0, inDraft: 0, awaiting: 0, accepted: 0
        };
        if (isLoading || !invoices || !quotes) {
            return { invoiceMetrics: defaultMetrics, quoteMetrics: defaultMetrics };
        }

        const invMetrics = {
            outstanding: invoices
                .filter(i => i.status === InvoiceStatus.Sent || i.status === InvoiceStatus.Overdue)
                .reduce((sum, i) => sum + i.amount, 0),
            overdue: invoices
                .filter(i => i.status === InvoiceStatus.Overdue)
                .reduce((sum, i) => sum + i.amount, 0),
            inDraft: invoices.filter(i => i.status === InvoiceStatus.Draft).length,
        };

        const qMetrics = {
            awaiting: quotes
                .filter(q => q.status === QuoteStatus.Sent)
                .reduce((sum, q) => sum + q.amount, 0),
            accepted: quotes
                .filter(q => q.status === QuoteStatus.Accepted)
                .reduce((sum, q) => sum + q.amount, 0),
            inDraft: quotes.filter(q => q.status === QuoteStatus.Draft).length,
        };

        return { invoiceMetrics: invMetrics, quoteMetrics: qMetrics };
    }, [isLoading, invoices, quotes]);

    return (
        <Card className="h-full">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Invoices & Quotes</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Invoices */}
                <div>
                    <h4 className="font-medium text-gray-800">Invoices</h4>
                    <dl className="mt-4 space-y-4">
                        <MetricItem label="Outstanding" value={formatCurrency(invoiceMetrics.outstanding)} isLoading={isLoading} />
                        <MetricItem label="Overdue" value={formatCurrency(invoiceMetrics.overdue)} isLoading={isLoading} />
                        <MetricItem label="In Draft" value={isLoading ? '' : invoiceMetrics.inDraft} isLoading={isLoading} />
                    </dl>
                </div>
                {/* Quotes */}
                <div>
                    <h4 className="font-medium text-gray-800">Quotes</h4>
                    <dl className="mt-4 space-y-4">
                        <MetricItem label="Awaiting Response" value={formatCurrency(quoteMetrics.awaiting)} isLoading={isLoading} />
                        <MetricItem label="Accepted" value={formatCurrency(quoteMetrics.accepted)} isLoading={isLoading} />
                        <MetricItem label="In Draft" value={isLoading ? '' : quoteMetrics.inDraft} isLoading={isLoading} />
                    </dl>
                </div>
            </div>
        </Card>
    );
};

export default InvoiceQuoteSummary;