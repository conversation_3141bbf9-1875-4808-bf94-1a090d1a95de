import React, { useMemo } from 'react';
import Card from '../ui/Card';
import { ProfitLossData } from '../../types';
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { generatePAndL } from '../../lib/reportUtils';

interface ProfitLossSummaryProps {
  workspaceId: string;
}

const ProfitLossSummary: React.FC<ProfitLossSummaryProps> = ({ workspaceId }) => {
    const accounts = useQuery(api.accounts.getAccounts, { workspaceId: workspaceId as any });
    const journalEntries = useQuery(api.journalEntries.getJournalEntries, { workspaceId: workspaceId as any });
    const isLoading = accounts === undefined || journalEntries === undefined;

    const data = useMemo(() => {
        if (isLoading || !accounts || !journalEntries) return [];

        // For dashboard, show last 30 days
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(endDate.getDate() - 30);
        const pnl = generatePAndL(accounts, journalEntries, startDate.toISOString().split('T')[0], endDate.toISOString().split('T')[0]);
        
        const result: ProfitLossData[] = [];
        const totalRevenue = Object.values(pnl.revenue).reduce((sum, cat) => sum + cat.total, 0);
        if(totalRevenue > 0) result.push({ category: 'Revenue', amount: totalRevenue });
        
        Object.values(pnl.expenses).forEach(cat => {
            cat.accounts.forEach(acc => {
                if (acc.balance > 0) {
                    result.push({ category: acc.name, amount: -acc.balance });
                }
            });
        });
        
        return result;
    }, [isLoading, accounts, journalEntries]);

    const formatCurrency = (amount: number) => {
        const value = new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);
        return amount < 0 ? `${value.replace('$', '-$').replace('(', '').replace(')','')}` : value;
    };

    const netTotal = data.reduce((acc, item) => acc + item.amount, 0);

    return (
        <Card className="h-96 flex flex-col">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">P&L Summary (Last 30 Days)</h3>
            {isLoading ? (
                <div className="flex-1 space-y-3">
                    {[...Array(5)].map((_, i) => <div key={i} className="h-6 bg-gray-200 rounded animate-pulse" />)}
                </div>
            ) : (
                <div className="flex-1 overflow-y-auto">
                    <ul className="divide-y divide-gray-200">
                        {data.map(item => (
                            <li key={item.category} className="py-3 flex justify-between items-center text-sm">
                                <span className="text-gray-600">{item.category}</span>
                                <span className={`font-medium ${item.amount >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                    {formatCurrency(item.amount)}
                                </span>
                            </li>
                        ))}
                    </ul>
                </div>
            )}
            <div className="border-t border-gray-200 pt-4 mt-4">
                <div className="flex justify-between items-center font-bold text-base">
                    <span>Net Total</span>
                     {isLoading ? <div className="h-6 w-24 bg-gray-200 rounded animate-pulse" /> : (
                        <span className={netTotal >= 0 ? 'text-green-600' : 'text-red-600'}>{formatCurrency(netTotal)}</span>
                     )}
                </div>
            </div>
        </Card>
    );
};

export default ProfitLossSummary;