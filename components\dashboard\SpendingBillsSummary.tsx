import React, { useMemo } from 'react';
import Card from '../ui/Card';
import { Bill, BillStatus } from '../../types';
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";

interface SpendingBillsSummaryProps {
    workspaceId: string;
}

const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);
};

const SpendingBillsSummary: React.FC<SpendingBillsSummaryProps> = ({ workspaceId }) => {
    const bills = useQuery(api.bills.getBills, { workspaceId: workspaceId as any });
    const isLoading = bills === undefined;

    const { spendingByCategory, totalSpending, upcomingBills } = useMemo((): {
        spendingByCategory: Record<string, number>;
        totalSpending: number;
        upcomingBills: Bill[];
    } => {
        if (!bills) return { spendingByCategory: {}, totalSpending: 0, upcomingBills: [] };
        // NOTE: In a real app, this would be filtered by the current month.
        const paidBills = bills.filter(b => b.status === BillStatus.Paid);
        
        const spending = paidBills.reduce<Record<string, number>>((acc, bill) => {
            acc[bill.category] = (acc[bill.category] || 0) + bill.amount;
            return acc;
        }, {});
        
        const total = Object.values(spending).reduce((sum, amount) => sum + amount, 0);

        const upcoming = bills
            .filter(b => b.status === BillStatus.Unpaid)
            .sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime())
            .slice(0, 3);

        return { spendingByCategory: spending, totalSpending: total, upcomingBills: upcoming };
    }, [bills]);

    return (
        <Card className="h-full flex flex-col">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Spending & Upcoming Bills</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-8 flex-grow">
                {/* Spending */}
                <div>
                    <h4 className="font-medium text-gray-800">Spending this month</h4>
                    <div className="mt-4 space-y-3">
                        {isLoading ? (
                            [...Array(3)].map((_, i) => <div key={i} className="h-8 bg-gray-200 rounded animate-pulse" />)
                        ) : Object.keys(spendingByCategory).length > 0 ? (
                             Object.entries(spendingByCategory).map(([category, amount]) => (
                                <div key={category}>
                                    <div className="flex justify-between text-sm mb-1">
                                        <span className="text-gray-600">{category}</span>
                                        <span className="font-medium">{formatCurrency(amount)}</span>
                                    </div>
                                    <div className="w-full bg-gray-200 rounded-full h-2">
                                        <div 
                                            className="bg-gray-800 h-2 rounded-full" 
                                            style={{ width: `${totalSpending > 0 ? (amount / totalSpending) * 100 : 0}%`}}
                                        ></div>
                                    </div>
                                </div>
                            ))
                        ) : (
                            <p className="text-sm text-gray-500 italic mt-2">No spending recorded this month.</p>
                        )}
                    </div>
                </div>

                {/* Upcoming Bills */}
                <div>
                    <h4 className="font-medium text-gray-800">Upcoming Bills</h4>
                     <div className="mt-4 space-y-3">
                        {isLoading ? (
                            [...Array(3)].map((_, i) => <div key={i} className="h-8 bg-gray-200 rounded animate-pulse" />)
                        ) : upcomingBills.length > 0 ? (
                            upcomingBills.map(bill => (
                                <div key={bill._id} className="flex justify-between items-center text-sm">
                                    <div>
                                        <p className="text-gray-800 font-medium">{bill.vendor}</p>
                                        <p className="text-gray-500">Due: {new Date(bill.dueDate).toLocaleDateString()}</p>
                                    </div>
                                    <span className="font-semibold">{formatCurrency(bill.amount)}</span>
                                </div>
                            ))
                        ) : (
                             <p className="text-sm text-gray-500 italic mt-2">No upcoming bills. You're all caught up!</p>
                        )}
                    </div>
                </div>
            </div>
        </Card>
    );
};

export default SpendingBillsSummary;