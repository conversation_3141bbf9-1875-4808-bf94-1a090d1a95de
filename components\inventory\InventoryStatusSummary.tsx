import React, { useMemo } from 'react';
import { Product } from '../../types';
import Card from '../ui/Card';

interface StatCardProps {
  label: string;
  value: string | number;
  isLoading: boolean;
}

const StatCard: React.FC<StatCardProps> = ({ label, value, isLoading }) => (
  <Card padding="sm">
    <dt className="text-sm font-medium text-gray-500 truncate">{label}</dt>
    {isLoading ? (
        <dd className="mt-1 h-8 w-28 bg-gray-200 rounded animate-pulse"></dd>
    ) : (
        <dd className="mt-1 text-3xl font-semibold text-gray-900">{value}</dd>
    )}
  </Card>
);

interface InventoryStatusSummaryProps {
  products: Product[];
  isLoading: boolean;
}

const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);
};

const LOW_STOCK_THRESHOLD = 10;

const InventoryStatusSummary: React.FC<InventoryStatusSummaryProps> = ({ products, isLoading }) => {
    const stats = useMemo(() => {
        if (!products) {
            return {
                totalProducts: 0,
                lowStockProducts: 0,
                totalValue: 0
            };
        }

        const totalProducts = products.length;
        const totalValue = products.reduce((sum, p) => sum + (p.stock * p.unitPrice), 0);
        const lowStockItems = products.filter(p => p.stock < LOW_STOCK_THRESHOLD).length;
        
        return { totalProducts, totalValue, lowStockItems };
    }, [products]);

    return (
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-3">
            <StatCard label="Total Products" value={stats.totalProducts} isLoading={isLoading} />
            <StatCard label="Total Stock Value" value={formatCurrency(stats.totalValue)} isLoading={isLoading} />
            <StatCard label="Low Stock Items" value={stats.lowStockItems} isLoading={isLoading} />
        </div>
    );
};

export default InventoryStatusSummary;