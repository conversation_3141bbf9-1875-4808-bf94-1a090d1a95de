import React, { useState, useEffect, useMemo } from 'react';
import { Invoice, InvoiceItem, InvoiceStatus, Client, Product, TaxRate } from '../../types';
import Modal from '../ui/Modal';
import Input from '../ui/Input';
import Button from '../ui/Button';
import Select from '../ui/Select';
import ProductSelector from '../ui/ProductSelector';

interface InvoiceFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (invoice: Omit<Invoice, '_id' | '_creationTime' | 'workspaceId' | 'amount' | 'taxRate'>) => void;
  invoice: Invoice | null;
  clients: Client[];
  products: Product[];
  taxRates: TaxRate[];
}

interface FormErrors {
  clientId?: string;
  issueDate?: string;
  dueDate?: string;
  items?: string[];
  general?: string;
}

const today = new Date().toISOString().split('T')[0];
const defaultDueDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]; // 30 days from today

const emptyItem: InvoiceItem = { description: '', quantity: 1, unitPrice: 0, total: 0 };

// Generate invoice number
const generateInvoiceNumber = () => {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `INV-${year}${month}-${random}`;
};

const InvoiceFormModal: React.FC<InvoiceFormModalProps> = ({ isOpen, onClose, onSave, invoice, clients, products, taxRates }) => {

  const newInvoiceTemplate = useMemo(() => {
    const defaultTax = taxRates.find(t => t.isDefault);
    return {
      clientId: '',
      invoiceNumber: generateInvoiceNumber(),
      issueDate: today,
      dueDate: defaultDueDate,
      status: InvoiceStatus.Draft,
      items: [emptyItem],
      taxId: defaultTax?._id,
      notes: '',
      discountType: 'percentage' as 'percentage' | 'fixed',
      discountValue: 0,
    }
  }, [taxRates]);

  const [formData, setFormData] = useState<Omit<Invoice, '_id' | '_creationTime' | 'workspaceId' | 'amount' | 'taxRate'>>({ ...newInvoiceTemplate });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const isEditing = useMemo(() => !!invoice, [invoice]);

  useEffect(() => {
    if (isOpen) {
      setErrors({});
      setIsSubmitting(false);
      if (invoice) {
        const { _id, _creationTime, workspaceId, amount, taxRate, ...editableData } = invoice;
        setFormData({ ...editableData });
      } else {
        setFormData({ ...newInvoiceTemplate, items: [{ ...emptyItem }] });
      }
    }
  }, [invoice, isOpen, newInvoiceTemplate]);

  // Validation functions
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.clientId) {
      newErrors.clientId = 'Please select a client';
    }

    if (!formData.issueDate) {
      newErrors.issueDate = 'Issue date is required';
    }

    if (!formData.dueDate) {
      newErrors.dueDate = 'Due date is required';
    } else if (new Date(formData.dueDate) < new Date(formData.issueDate)) {
      newErrors.dueDate = 'Due date cannot be before issue date';
    }

    // Validate items
    const itemErrors: string[] = [];
    formData.items.forEach((item, index) => {
      if (!item.description.trim()) {
        itemErrors[index] = 'Description is required';
      } else if (item.quantity <= 0) {
        itemErrors[index] = 'Quantity must be greater than 0';
      } else if (item.unitPrice < 0) {
        itemErrors[index] = 'Unit price cannot be negative';
      }
    });

    if (itemErrors.some(error => error)) {
      newErrors.items = itemErrors;
    }

    if (formData.items.length === 0) {
      newErrors.general = 'At least one item is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear specific field error when user starts typing
    if (errors[name as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  const handleItemChange = (index: number, e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const items = [...formData.items];
    const item = { ...items[index] };

    if (name === 'description') {
      item.description = value;
    } else if (name === 'quantity') {
      item.quantity = Math.max(0, parseFloat(value) || 0);
    } else if (name === 'unitPrice') {
      item.unitPrice = Math.max(0, parseFloat(value) || 0);
    }

    item.total = item.quantity * item.unitPrice;
    items[index] = item;
    setFormData(prev => ({ ...prev, items }));

    // Clear item-specific errors
    if (errors.items && errors.items[index]) {
      const newItemErrors = [...(errors.items || [])];
      newItemErrors[index] = '';
      setErrors(prev => ({ ...prev, items: newItemErrors }));
    }
  };

  const handleProductSelect = (index: number, description: string, product?: Product) => {
    const items = [...formData.items];
    const item = { ...items[index] };

    item.description = description;
    if (product) {
      item.unitPrice = product.unitPrice;
      item.productId = product._id;
    } else {
      item.productId = undefined;
    }

    item.total = item.quantity * item.unitPrice;
    items[index] = item;
    setFormData(prev => ({ ...prev, items }));
  };

  const addItem = () => {
    setFormData(prev => ({ ...prev, items: [...prev.items, { ...emptyItem }] }));
  };

  const removeItem = (index: number) => {
    if (formData.items.length <= 1) return;
    const items = formData.items.filter((_, i) => i !== index);
    setFormData(prev => ({ ...prev, items }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onSave(formData);
      onClose();
    } catch (error) {
      setErrors({ general: 'Failed to save invoice. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const subtotal = formData.items.reduce((sum, item) => sum + item.total, 0);

  // Calculate discount
  const discountAmount = formData.discountType === 'percentage'
    ? subtotal * ((formData.discountValue || 0) / 100)
    : (formData.discountValue || 0);

  const discountedSubtotal = subtotal - discountAmount;

  // Calculate tax on discounted amount
  const selectedTaxRate = taxRates.find(t => t._id === formData.taxId)?.rate || 0;
  const taxAmount = discountedSubtotal * selectedTaxRate;
  const totalAmount = discountedSubtotal + taxAmount;
  
  const formatCurrency = (amount: number) => new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="3xl">
      <div className="relative bg-white">
        {/* Premium Header with Gradient */}
        <div className="relative bg-gradient-to-br from-gray-900 via-gray-800 to-black text-white">
          <div className="absolute inset-0 opacity-20" style={{backgroundImage: "url('data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"none\" fill-rule=\"evenodd\"%3E%3Cg fill=\"%23ffffff\" fill-opacity=\"0.03\"%3E%3Ccircle cx=\"30\" cy=\"30\" r=\"2\"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')"}}></div>
          <div className="relative px-6 py-5">
            <div className="flex justify-between items-start">
              <div className="space-y-1">
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-white/10 backdrop-blur-sm rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <div>
                    <h1 className="text-lg font-bold tracking-tight">
                      {isEditing ? 'Edit Invoice' : 'Create New Invoice'}
                    </h1>
                    <p className="text-white/70 text-xs font-medium">
                      {isEditing ? 'Update invoice details' : 'Design a professional invoice'}
                    </p>
                  </div>
                </div>
              </div>
              <div className="text-right space-y-1">
                <div className="text-white/60 text-xs font-medium uppercase tracking-wider">Invoice Number</div>
                <div className="text-lg font-bold text-white font-mono tracking-wide bg-white/10 backdrop-blur-sm px-3 py-1.5 rounded-lg border border-white/20">
                  {formData.invoiceNumber}
                </div>
              </div>
            </div>

            {/* Status Badge */}
            <div className="mt-4 flex items-center justify-between">
              <div className="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-semibold bg-white/10 backdrop-blur-sm text-white border border-white/20">
                <div className="w-1.5 h-1.5 bg-emerald-400 rounded-full mr-2 animate-pulse"></div>
                {formData.status}
              </div>
              <button
                type="button"
                onClick={onClose}
                title="Close modal"
                className="text-white/60 hover:text-white hover:bg-white/10 rounded-lg p-1.5 transition-all duration-200"
              >
                <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="h-full flex flex-col">
          <div className="flex-1 overflow-y-auto">
            <div className="p-6 space-y-6">
              {/* General Error */}
              {errors.general && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3 shadow-sm">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-4 w-4 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-2">
                      <p className="text-sm text-red-800">{errors.general}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Basic Information Section */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2 pb-3 border-b border-gray-100">
                  <div className="w-6 h-6 bg-gray-900 rounded-md flex items-center justify-center">
                    <svg className="w-3 h-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <h4 className="text-base font-bold text-gray-900">Client & Invoice Details</h4>
                </div>
                <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow duration-200">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-1">
                      <Select
                        label="Customer *"
                        name="clientId"
                        value={formData.clientId}
                        onChange={handleChange}
                        required
                        className={`transition-all duration-200 ${errors.clientId ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : 'focus:ring-2 focus:ring-gray-900/20 focus:border-gray-900'}`}
                      >
                        <option value="" disabled>Select a client</option>
                        {clients.map(client => (
                          <option key={client._id} value={client._id}>{client.name}</option>
                        ))}
                      </Select>
                      {errors.clientId && (
                        <div className="flex items-center space-x-1 text-red-600">
                          <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <p className="text-xs font-medium">{errors.clientId}</p>
                        </div>
                      )}
                    </div>

                    <div className="space-y-1">
                      <Select
                        label="Status"
                        name="status"
                        value={formData.status}
                        onChange={handleChange}
                        className="focus:ring-2 focus:ring-gray-900/20 focus:border-gray-900 transition-all duration-200"
                      >
                        {Object.values(InvoiceStatus).map(s => (
                          <option key={s} value={s}>{s}</option>
                        ))}
                      </Select>
                    </div>

                    <div className="space-y-1">
                      <Input
                        label="Issue Date *"
                        type="date"
                        name="issueDate"
                        value={formData.issueDate}
                        onChange={handleChange}
                        required
                        className={`transition-all duration-200 ${errors.issueDate ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : 'focus:ring-2 focus:ring-gray-900/20 focus:border-gray-900'}`}
                      />
                      {errors.issueDate && (
                        <div className="flex items-center space-x-1 text-red-600">
                          <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <p className="text-xs font-medium">{errors.issueDate}</p>
                        </div>
                      )}
                    </div>

                    <div className="space-y-1">
                      <Input
                        label="Due Date *"
                        type="date"
                        name="dueDate"
                        value={formData.dueDate}
                        onChange={handleChange}
                        required
                        className={`transition-all duration-200 ${errors.dueDate ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : 'focus:ring-2 focus:ring-gray-900/20 focus:border-gray-900'}`}
                      />
                      {errors.dueDate && (
                        <div className="flex items-center space-x-1 text-red-600">
                          <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <p className="text-xs font-medium">{errors.dueDate}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Items Section */}
              <div className="space-y-4">
                <div className="flex items-center justify-between pb-3 border-b border-gray-100">
                  <div className="flex items-center space-x-2">
                    <div className="w-6 h-6 bg-gray-900 rounded-md flex items-center justify-center">
                      <svg className="w-3 h-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                      </svg>
                    </div>
                    <h4 className="text-base font-bold text-gray-900">Invoice Items</h4>
                  </div>
                  <Button
                    type="button"
                    variant="primary"
                    size="sm"
                    onClick={addItem}
                    className="flex items-center gap-1.5 bg-gray-900 hover:bg-gray-800 shadow-md hover:shadow-lg transition-all duration-200 text-xs px-3 py-1.5"
                  >
                    <svg className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Add Item
                  </Button>
                </div>

                <div className="bg-white border border-gray-200 rounded-lg shadow-md overflow-hidden">
                  {/* Compact Table Header */}
                  <div className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 px-4 py-3">
                    <div className="grid grid-cols-12 gap-3 text-xs font-bold text-white uppercase tracking-wider">
                      <div className="col-span-5 flex items-center space-x-1">
                        <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                        </svg>
                        <span>Product/Description</span>
                      </div>
                      <div className="col-span-2 text-center">Qty</div>
                      <div className="col-span-2 text-center">Price</div>
                      <div className="col-span-2 text-center">Total</div>
                      <div className="col-span-1 text-center">Action</div>
                    </div>
                  </div>

                  {/* Compact Table Body */}
                  <div className="divide-y divide-gray-50">
                    {formData.items.map((item, index) => (
                      <div key={index} className="group hover:bg-gray-50/50 transition-all duration-200">
                        <div className="px-4 py-3">
                          <div className="grid grid-cols-12 gap-3 items-center">
                            {/* Product/Description Column */}
                            <div className="col-span-5">
                              <div className="space-y-1">
                                <div className="relative">
                                  <ProductSelector
                                    value={item.description}
                                    onChange={(value, product) => handleProductSelect(index, value, product)}
                                    products={products}
                                    placeholder="Search products or enter description..."
                                  />
                                </div>
                                {errors.items && errors.items[index] && (
                                  <div className="flex items-center gap-1 text-red-600 bg-red-50 px-2 py-1 rounded border border-red-200">
                                    <svg className="h-3 w-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <p className="text-xs font-medium">{errors.items[index]}</p>
                                  </div>
                                )}
                              </div>
                            </div>

                            {/* Quantity Column */}
                            <div className="col-span-2">
                              <div className="relative group">
                                <input
                                  type="number"
                                  name="quantity"
                                  value={item.quantity}
                                  onChange={(e) => handleItemChange(index, e)}
                                  min="0"
                                  step="1"
                                  placeholder="0"
                                  className="w-full px-3 py-2 text-center border border-gray-300 rounded-lg shadow-sm font-medium text-gray-900 bg-white focus:ring-2 focus:ring-gray-900/20 focus:border-gray-900 transition-all duration-200 text-sm"
                                />
                              </div>
                            </div>

                            {/* Unit Price Column */}
                            <div className="col-span-2">
                              <div className="relative group">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                  <span className="text-gray-500 text-sm font-medium">$</span>
                                </div>
                                <input
                                  type="number"
                                  name="unitPrice"
                                  value={item.unitPrice}
                                  onChange={(e) => handleItemChange(index, e)}
                                  min="0"
                                  step="0.01"
                                  placeholder="0.00"
                                  className="w-full pl-7 pr-3 py-2 text-center border border-gray-300 rounded-lg shadow-sm font-medium text-gray-900 bg-white focus:ring-2 focus:ring-gray-900/20 focus:border-gray-900 transition-all duration-200 text-sm"
                                />
                              </div>
                            </div>

                            {/* Total Column */}
                            <div className="col-span-2">
                              <div className="bg-gradient-to-r from-gray-900 to-gray-800 rounded-lg px-3 py-2 text-center shadow-md">
                                <span className="font-bold text-white text-sm tracking-wide">
                                  {formatCurrency(item.total)}
                                </span>
                              </div>
                            </div>

                            {/* Actions Column */}
                            <div className="col-span-1 flex justify-center">
                              <button
                                type="button"
                                onClick={() => removeItem(index)}
                                disabled={formData.items.length <= 1}
                                className="p-2 rounded-lg text-gray-400 hover:text-red-600 hover:bg-red-50 transition-all duration-200 disabled:opacity-30 disabled:cursor-not-allowed group-hover:opacity-100"
                                title="Remove item"
                              >
                                <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Premium Empty State */}
                  {formData.items.length === 0 && (
                    <div className="px-8 py-16 text-center bg-gradient-to-br from-gray-50 to-white">
                      <div className="mx-auto h-20 w-20 bg-gradient-to-br from-gray-900 to-gray-700 rounded-2xl flex items-center justify-center mb-6 shadow-lg">
                        <svg className="h-10 w-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                      </div>
                      <h3 className="text-xl font-bold text-gray-900 mb-3">No items added yet</h3>
                      <p className="text-gray-600 mb-6 max-w-sm mx-auto">Start building your professional invoice by adding products or services</p>
                      <Button
                        type="button"
                        variant="primary"
                        onClick={addItem}
                        className="bg-gray-900 hover:bg-gray-800 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
                      >
                        Add Your First Item
                      </Button>
                    </div>
                  )}

                  {/* Compact Table Footer */}
                  {formData.items.length > 0 && (
                    <div className="bg-gradient-to-r from-gray-900 to-gray-800 px-4 py-2.5">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center space-x-1.5 text-white/80">
                          <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                          </svg>
                          <span className="text-xs font-medium">{formData.items.length} item{formData.items.length !== 1 ? 's' : ''}</span>
                        </div>
                        <div className="text-white">
                          <span className="text-xs font-medium">Subtotal: </span>
                          <span className="text-sm font-bold">{formatCurrency(subtotal)}</span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Notes Section */}
              <div className="space-y-6">
                <div className="flex items-center space-x-3 pb-4 border-b border-gray-100">
                  <div className="w-8 h-8 bg-gray-900 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-1l-4 4z" />
                    </svg>
                  </div>
                  <h4 className="text-xl font-bold text-gray-900">Additional Information</h4>
                </div>
                <div className="bg-white border border-gray-200 rounded-2xl p-6 shadow-sm hover:shadow-md transition-shadow duration-200">
                  <label className="block text-sm font-semibold text-gray-700 mb-3">
                    Notes (Optional)
                  </label>
                  <textarea
                    name="notes"
                    value={formData.notes || ''}
                    onChange={handleChange}
                    rows={4}
                    className="block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-900/20 focus:border-gray-900 sm:text-sm transition-all duration-200 resize-none"
                    placeholder="Add any additional notes, terms, or special instructions for this invoice..."
                  />
                </div>
              </div>

              {/* Compact Totals Section */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2 pb-3 border-b border-gray-100">
                  <div className="w-6 h-6 bg-gray-900 rounded-md flex items-center justify-center">
                    <svg className="w-3 h-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 0v5a2 2 0 01-2 2H9a2 2 0 01-2-2V7a2 2 0 012-2h6a2 2 0 012 2z" />
                    </svg>
                  </div>
                  <h4 className="text-base font-bold text-gray-900">Pricing & Calculations</h4>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  {/* Discount & Tax Controls */}
                  <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow duration-200">
                    <h5 className="text-sm font-bold text-gray-900 mb-4 flex items-center space-x-1.5">
                      <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                      </svg>
                      <span>Discount & Tax</span>
                    </h5>

                    <div className="space-y-3">
                      {/* Discount Type */}
                      <div>
                        <label className="block text-xs font-semibold text-gray-700 mb-2">Discount Type</label>
                        <div className="grid grid-cols-2 gap-2">
                          <label className="flex items-center p-2 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors duration-200">
                            <input
                              type="radio"
                              name="discountType"
                              value="percentage"
                              checked={formData.discountType === 'percentage'}
                              onChange={handleChange}
                              className="h-3 w-3 text-gray-900 focus:ring-gray-900 border-gray-300"
                            />
                            <span className="ml-2 text-xs font-medium text-gray-700">Percentage (%)</span>
                          </label>
                          <label className="flex items-center p-2 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors duration-200">
                            <input
                              type="radio"
                              name="discountType"
                              value="fixed"
                              checked={formData.discountType === 'fixed'}
                              onChange={handleChange}
                              className="h-3 w-3 text-gray-900 focus:ring-gray-900 border-gray-300"
                            />
                            <span className="ml-2 text-xs font-medium text-gray-700">Fixed Amount ($)</span>
                          </label>
                        </div>
                      </div>

                      {/* Discount Value */}
                      <div>
                        <label className="block text-xs font-semibold text-gray-700 mb-2">
                          Discount {formData.discountType === 'percentage' ? 'Percentage' : 'Amount'}
                        </label>
                        <div className="relative">
                          {formData.discountType === 'fixed' && (
                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                              <span className="text-gray-500 text-sm font-medium">$</span>
                            </div>
                          )}
                          <input
                            type="number"
                            name="discountValue"
                            value={formData.discountValue}
                            onChange={handleChange}
                            min="0"
                            max={formData.discountType === 'percentage' ? 100 : undefined}
                            step={formData.discountType === 'percentage' ? 1 : 0.01}
                            placeholder="0"
                            className={`w-full ${formData.discountType === 'fixed' ? 'pl-7' : 'pr-7'} py-2 border border-gray-300 rounded-lg shadow-sm font-medium text-gray-900 bg-white focus:ring-2 focus:ring-gray-900/20 focus:border-gray-900 transition-all duration-200 text-sm`}
                          />
                          {formData.discountType === 'percentage' && (
                            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                              <span className="text-gray-500 text-sm font-medium">%</span>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Tax Selection */}
                      <div>
                        <label className="block text-xs font-semibold text-gray-700 mb-2">Tax Rate</label>
                        <Select
                          name="taxId"
                          value={formData.taxId}
                          onChange={handleChange}
                          className="w-full focus:ring-2 focus:ring-gray-900/20 focus:border-gray-900 transition-all duration-200"
                        >
                          {taxRates.map(t => (
                            <option key={t._id} value={t._id}>
                              {t.name} ({(t.rate * 100).toFixed(2)}%)
                            </option>
                          ))}
                        </Select>
                      </div>
                    </div>
                  </div>

                  {/* Compact Totals Display */}
                  <div className="space-y-3">
                    <h5 className="text-sm font-bold text-gray-900 flex items-center space-x-1.5">
                      <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 0v5a2 2 0 01-2 2H9a2 2 0 01-2-2V7a2 2 0 012-2h6a2 2 0 012 2z" />
                      </svg>
                      <span>Invoice Summary</span>
                    </h5>

                    <div className="bg-gradient-to-br from-white to-gray-50 rounded-lg border border-gray-200 p-4 space-y-3 shadow-md">
                      {/* Subtotal */}
                      <div className="flex justify-between items-center pb-2 border-b border-gray-100">
                        <span className="text-xs font-semibold text-gray-600 uppercase tracking-wide">Subtotal</span>
                        <span className="text-sm font-bold text-gray-900">{formatCurrency(subtotal)}</span>
                      </div>

                      {/* Discount */}
                      {discountAmount > 0 && (
                        <div className="flex justify-between items-center pb-2 border-b border-gray-100">
                          <span className="text-xs font-semibold text-emerald-600 uppercase tracking-wide">
                            Discount {formData.discountType === 'percentage' ? `(${formData.discountValue}%)` : ''}
                          </span>
                          <span className="text-sm font-bold text-emerald-600">-{formatCurrency(discountAmount)}</span>
                        </div>
                      )}

                      {/* Discounted Subtotal */}
                      {discountAmount > 0 && (
                        <div className="flex justify-between items-center pb-2 border-b border-gray-100">
                          <span className="text-xs font-semibold text-gray-600 uppercase tracking-wide">After discount</span>
                          <span className="text-sm font-bold text-gray-900">{formatCurrency(discountedSubtotal)}</span>
                        </div>
                      )}

                      {/* Tax */}
                      <div className="flex justify-between items-center pb-2 border-b border-gray-100">
                        <span className="text-xs font-semibold text-gray-600 uppercase tracking-wide">
                          {taxRates.find(t => t._id === formData.taxId)?.name || 'Tax'} ({(selectedTaxRate * 100).toFixed(2)}%)
                        </span>
                        <span className="text-sm font-bold text-gray-900">{formatCurrency(taxAmount)}</span>
                      </div>

                      {/* Total */}
                      <div className="bg-gradient-to-r from-gray-900 to-gray-800 rounded-lg p-3 -m-1 mt-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-bold text-white uppercase tracking-wide">Total Amount</span>
                          <span className="text-lg font-bold text-white">{formatCurrency(totalAmount)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>

        {/* Compact Footer */}
        <div className="flex-shrink-0 bg-gradient-to-r from-gray-50 to-white border-t border-gray-200 px-6 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-1.5 text-gray-600">
              <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-xs font-medium">
                {isEditing ? 'Update invoice details' : 'All fields marked with * are required'}
              </span>
            </div>
            <div className="flex space-x-3">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isSubmitting}
                className="px-4 py-2 border border-gray-300 hover:border-gray-400 transition-all duration-200 text-sm"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="primary"
                disabled={isSubmitting}
                className="min-w-[120px] px-6 py-2 bg-gray-900 hover:bg-gray-800 shadow-md hover:shadow-lg transition-all duration-200 text-sm"
              >
                {isSubmitting ? (
                  <div className="flex items-center space-x-1.5">
                    <svg className="animate-spin h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span className="font-medium">Saving...</span>
                  </div>
                ) : (
                  <div className="flex items-center space-x-1.5">
                    <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="font-medium">
                      {isEditing ? 'Update Invoice' : 'Create Invoice'}
                    </span>
                  </div>
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default InvoiceFormModal;