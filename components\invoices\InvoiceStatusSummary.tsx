import React, { useMemo } from 'react';
import { Invoice, InvoiceStatus } from '../../types';
import Card from '../ui/Card';

interface StatCardProps {
  label: string;
  value: string;
  isLoading: boolean;
}

const StatCard: React.FC<StatCardProps> = ({ label, value, isLoading }) => (
  <Card padding="sm">
    <dt className="text-sm font-medium text-gray-500 truncate">{label}</dt>
    {isLoading ? (
        <dd className="mt-1 h-8 w-28 bg-gray-200 rounded animate-pulse"></dd>
    ) : (
        <dd className="mt-1 text-3xl font-semibold text-gray-900">{value}</dd>
    )}
  </Card>
);

interface InvoiceStatusSummaryProps {
  invoices: Invoice[] | undefined;
  isLoading: boolean;
}

const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);
};

const InvoiceStatusSummary: React.FC<InvoiceStatusSummaryProps> = ({ invoices, isLoading }) => {
    const stats = useMemo(() => {
        // Provide default empty array if invoices is undefined
        const safeInvoices = invoices || [];

        const totalValue = safeInvoices.reduce((sum, i) => sum + i.amount, 0);
        const outstanding = safeInvoices
            .filter(i => i.status === InvoiceStatus.Sent || i.status === InvoiceStatus.Overdue)
            .reduce((sum, i) => sum + i.amount, 0);
        const overdue = safeInvoices
            .filter(i => i.status === InvoiceStatus.Overdue)
            .reduce((sum, i) => sum + i.amount, 0);

        return { totalValue, outstanding, overdue };
    }, [invoices]);

    return (
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-3">
            <StatCard label="Total Value" value={formatCurrency(stats.totalValue)} isLoading={isLoading} />
            <StatCard label="Outstanding" value={formatCurrency(stats.outstanding)} isLoading={isLoading} />
            <StatCard label="Overdue" value={formatCurrency(stats.overdue)} isLoading={isLoading} />
        </div>
    );
};

export default InvoiceStatusSummary;
