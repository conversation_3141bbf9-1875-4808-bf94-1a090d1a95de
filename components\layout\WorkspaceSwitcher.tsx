import React from 'react';
import { User, Workspace } from '../../types';
import Dropdown from '../ui/Dropdown';

interface WorkspaceSwitcherProps {
  user: User | null;
  workspaces: Workspace[];
  activeWorkspace: Workspace | null;
  onSwitchWorkspace: (workspaceId: string) => void;
  onLogout: () => void;
}

const WorkspaceSwitcher: React.FC<WorkspaceSwitcherProps> = ({ user, workspaces, activeWorkspace, onSwitchWorkspace, onLogout }) => {
  if (!activeWorkspace || !user) {
    return (
      <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse" />
    );
  }

  const getInitials = (name: string) => {
    const names = name.split(' ');
    if (names.length > 1) {
      return `${names[0][0]}${names[names.length - 1][0]}`.toUpperCase();
    }
    return name.substring(0, 2).toUpperCase();
  };

  return (
    <Dropdown
      trigger={
        <div className="flex items-center space-x-3 cursor-pointer p-1 rounded-md hover:bg-gray-100">
          <div className="w-8 h-8 bg-gray-800 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0">
            {getInitials(activeWorkspace.name)}
          </div>
          <div className="hidden md:block text-left">
            <p className="text-sm font-semibold truncate max-w-[120px]">{activeWorkspace.name}</p>
            <p className="text-xs text-gray-500">Workspace</p>
          </div>
           <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 hidden md:block" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 3a1 1 0 01.707.293l3 3a1 1 0 01-1.414 1.414L10 5.414 7.707 7.707a1 1 0 01-1.414-1.414l3-3A1 1 0 0110 3zm-3.707 9.293a1 1 0 011.414 0L10 14.586l2.293-2.293a1 1 0 011.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
           </svg>
        </div>
      }
    >
      <div className="py-1">
        <div className="px-4 py-2 border-b">
            <p className="text-sm text-gray-500">Signed in as</p>
            <p className="text-sm font-medium text-gray-900 truncate">{user.name}</p>
        </div>
        <div className="py-1">
             <p className="px-4 pt-2 pb-1 text-xs font-semibold text-gray-500 uppercase">Workspaces</p>
            {workspaces.map(workspace => (
                <button
                    key={workspace._id}
                    onClick={() => onSwitchWorkspace(workspace._id)}
                    className={`flex items-center w-full text-left px-4 py-2 text-sm ${
                        workspace._id === activeWorkspace._id
                        ? 'bg-gray-100 text-gray-900'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                >
                    <div className="w-5 h-5 mr-3 bg-gray-700 text-white rounded-md flex items-center justify-center text-xs font-bold flex-shrink-0">
                        {getInitials(workspace.name)}
                    </div>
                   {workspace.name}
                   {workspace._id === activeWorkspace._id && (
                       <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 ml-auto" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                       </svg>
                   )}
                </button>
            ))}
        </div>
         <div className="border-t border-gray-100 my-1"></div>
         <button
           type="button"
           onClick={onLogout}
           className="flex items-center w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50"
         >
           <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
             <path fillRule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z" clipRule="evenodd" />
           </svg>
           Log out
         </button>
      </div>
    </Dropdown>
  );
};

export default WorkspaceSwitcher;