import React, { useMemo } from 'react';
import { PurchaseOrder, PurchaseOrderStatus } from '../../types';
import Card from '../ui/Card';

interface StatCardProps {
  label: string;
  value: string;
  isLoading: boolean;
}

const StatCard: React.FC<StatCardProps> = ({ label, value, isLoading }) => (
  <Card padding="sm">
    <dt className="text-sm font-medium text-gray-500 truncate">{label}</dt>
    {isLoading ? (
        <dd className="mt-1 h-8 w-28 bg-gray-200 rounded animate-pulse"></dd>
    ) : (
        <dd className="mt-1 text-3xl font-semibold text-gray-900">{value}</dd>
    )}
  </Card>
);

interface PurchaseStatusSummaryProps {
  purchaseOrders: PurchaseOrder[] | undefined;
  isLoading: boolean;
}

const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);
};

const PurchaseStatusSummary: React.FC<PurchaseStatusSummaryProps> = ({ purchaseOrders, isLoading }) => {
    const stats = useMemo(() => {
        // Provide default empty array if purchaseOrders is undefined
        const safePurchaseOrders = purchaseOrders || [];

        const totalValue = safePurchaseOrders
            .filter(po => po.status !== PurchaseOrderStatus.Cancelled)
            .reduce((sum, po) => sum + po.amount, 0);
        const openOrders = safePurchaseOrders
            .filter(po => po.status === PurchaseOrderStatus.Ordered)
            .reduce((sum, po) => sum + po.amount, 0);
        const inDraft = safePurchaseOrders
            .filter(po => po.status === PurchaseOrderStatus.Draft)
            .reduce((sum, po) => sum + po.amount, 0);

        return { totalValue, openOrders, inDraft };
    }, [purchaseOrders]);

    return (
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-3">
            <StatCard label="Total Value (Active)" value={formatCurrency(stats.totalValue)} isLoading={isLoading} />
            <StatCard label="Open Orders" value={formatCurrency(stats.openOrders)} isLoading={isLoading} />
            <StatCard label="In Draft" value={formatCurrency(stats.inDraft)} isLoading={isLoading} />
        </div>
    );
};

export default PurchaseStatusSummary;
