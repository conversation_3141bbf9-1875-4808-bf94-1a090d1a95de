import React, { useMemo } from 'react';
import { Quote, QuoteStatus } from '../../types';
import Card from '../ui/Card';

interface StatCardProps {
  label: string;
  value: string;
  isLoading: boolean;
}

const StatCard: React.FC<StatCardProps> = ({ label, value, isLoading }) => (
  <Card padding="sm">
    <dt className="text-sm font-medium text-gray-500 truncate">{label}</dt>
    {isLoading ? (
        <dd className="mt-1 h-8 w-28 bg-gray-200 rounded animate-pulse"></dd>
    ) : (
        <dd className="mt-1 text-3xl font-semibold text-gray-900">{value}</dd>
    )}
  </Card>
);

interface QuoteStatusSummaryProps {
  quotes: Quote[] | undefined;
  isLoading: boolean;
}

const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);
};

const QuoteStatusSummary: React.FC<QuoteStatusSummaryProps> = ({ quotes, isLoading }) => {
    const stats = useMemo(() => {
        // Provide default empty array if quotes is undefined
        const safeQuotes = quotes || [];

        const totalValue = safeQuotes
            .filter(q => q.status !== QuoteStatus.Declined)
            .reduce((sum, i) => sum + i.amount, 0);
        const awaiting = safeQuotes
            .filter(q => q.status === QuoteStatus.Sent)
            .reduce((sum, i) => sum + i.amount, 0);
        const accepted = safeQuotes
            .filter(q => q.status === QuoteStatus.Accepted)
            .reduce((sum, i) => sum + i.amount, 0);

        return { totalValue, awaiting, accepted };
    }, [quotes]);

    return (
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-3">
            <StatCard label="Total Open Value" value={formatCurrency(stats.totalValue)} isLoading={isLoading} />
            <StatCard label="Awaiting Response" value={formatCurrency(stats.awaiting)} isLoading={isLoading} />
            <StatCard label="Accepted" value={formatCurrency(stats.accepted)} isLoading={isLoading} />
        </div>
    );
};

export default QuoteStatusSummary;
