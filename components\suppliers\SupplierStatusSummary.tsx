import React, { useMemo } from 'react';
import { Supplier } from '../../types';
import Card from '../ui/Card';

interface StatCardProps {
  label: string;
  value: string | number;
  isLoading: boolean;
}

const StatCard: React.FC<StatCardProps> = ({ label, value, isLoading }) => (
  <Card padding="sm">
    <dt className="text-sm font-medium text-gray-500 truncate">{label}</dt>
    {isLoading ? (
        <dd className="mt-1 h-8 w-20 bg-gray-200 rounded animate-pulse"></dd>
    ) : (
        <dd className="mt-1 text-3xl font-semibold text-gray-900">{value}</dd>
    )}
  </Card>
);

interface SupplierStatusSummaryProps {
  suppliers: Supplier[];
  isLoading: boolean;
}

const SupplierStatusSummary: React.FC<SupplierStatusSummaryProps> = ({ suppliers, isLoading }) => {
    const stats = useMemo(() => {
        if (!suppliers) {
            return {
                totalSuppliers: 0,
                totalCategories: 0
            };
        }

        const totalSuppliers = suppliers.length;
        const totalCategories = new Set(suppliers.map(s => s.category).filter(Boolean)).size;

        return { totalSuppliers, totalCategories };
    }, [suppliers]);

    return (
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
            <StatCard label="Total Suppliers" value={stats.totalSuppliers} isLoading={isLoading} />
            <StatCard label="Unique Categories" value={stats.totalCategories} isLoading={isLoading} />
        </div>
    );
};

export default SupplierStatusSummary;
