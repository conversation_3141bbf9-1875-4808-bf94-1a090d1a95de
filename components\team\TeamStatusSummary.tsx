import React, { useMemo } from 'react';
import { TeamMember } from '../../types';
import Card from '../ui/Card';

interface StatCardProps {
  label: string;
  value: string | number;
  isLoading: boolean;
}

const StatCard: React.FC<StatCardProps> = ({ label, value, isLoading }) => (
  <Card padding="sm">
    <dt className="text-sm font-medium text-gray-500 truncate">{label}</dt>
    {isLoading ? (
        <dd className="mt-1 h-8 w-20 bg-gray-200 rounded animate-pulse"></dd>
    ) : (
        <dd className="mt-1 text-3xl font-semibold text-gray-900">{value}</dd>
    )}
  </Card>
);

interface TeamStatusSummaryProps {
  members: TeamMember[] | undefined;
  isLoading: boolean;
}

const TeamStatusSummary: React.FC<TeamStatusSummaryProps> = ({ members, isLoading }) => {
    const stats = useMemo(() => {
        // Provide default empty array if members is undefined
        const safeMembers = members || [];

        const totalMembers = safeMembers.length;
        const active = safeMembers.filter(m => m.status === 'Active').length;
        const invited = safeMembers.filter(m => m.status === 'Invited').length;

        return { totalMembers, active, invited };
    }, [members]);

    return (
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-3">
            <StatCard label="Total Members" value={stats.totalMembers} isLoading={isLoading} />
            <StatCard label="Active" value={stats.active} isLoading={isLoading} />
            <StatCard label="Invited" value={stats.invited} isLoading={isLoading} />
        </div>
    );
};

export default TeamStatusSummary;
