import React from 'react';
import Modal from './Modal';

interface ActionItem {
  label: string;
  onClick: () => void;
  variant?: 'default' | 'danger';
  icon?: React.ReactNode;
}

interface ActionModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  actions: ActionItem[];
}

const ActionModal: React.FC<ActionModalProps> = ({ isOpen, onClose, title, actions }) => {
  return (
    <Modal isOpen={isOpen} onClose={onClose} title={title}>
      <div className="space-y-2">
        {actions.map((action, index) => (
          <button
            key={index}
            onClick={() => {
              action.onClick();
              onClose();
            }}
            className={`w-full flex items-center px-4 py-3 text-left rounded-md transition-colors ${
              action.variant === 'danger'
                ? 'text-red-600 hover:bg-red-50 hover:text-red-700'
                : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
            }`}
          >
            {action.icon && (
              <span className="mr-3 flex-shrink-0">
                {action.icon}
              </span>
            )}
            <span className="font-medium">{action.label}</span>
          </button>
        ))}
      </div>
    </Modal>
  );
};

export default ActionModal;
