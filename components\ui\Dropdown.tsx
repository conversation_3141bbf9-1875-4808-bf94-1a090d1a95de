import React, { useState, useRef, useEffect } from 'react';

interface DropdownProps {
  trigger: React.ReactNode;
  children: React.ReactNode;
}

const Dropdown: React.FC<DropdownProps> = ({ trigger, children }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  useEffect(() => {
    if (isOpen && menuRef.current && dropdownRef.current) {
      // Use a timeout to ensure the menu is rendered before calculating position
      const timer = setTimeout(() => {
        if (!menuRef.current || !dropdownRef.current) return;

        const containerRect = dropdownRef.current.getBoundingClientRect();
        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;
        const menuWidth = 192; // w-48 = 12rem = 192px
        const menuHeight = 120; // Approximate height for 3 menu items

        // Reset all positioning styles
        menuRef.current.style.left = '';
        menuRef.current.style.right = '';
        menuRef.current.style.top = '';
        menuRef.current.style.bottom = '';
        menuRef.current.style.transform = '';

        // Determine horizontal position
        if (containerRect.right + menuWidth > windowWidth) {
          // Position to the left of the trigger
          menuRef.current.style.right = '0px';
        } else {
          // Position to the right of the trigger (default)
          menuRef.current.style.left = '0px';
        }

        // Determine vertical position
        if (containerRect.bottom + menuHeight > windowHeight) {
          // Position above the trigger
          menuRef.current.style.bottom = '100%';
          menuRef.current.style.marginBottom = '8px';
        } else {
          // Position below the trigger (default)
          menuRef.current.style.top = '100%';
          menuRef.current.style.marginTop = '8px';
        }
      }, 0);

      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  return (
    <div className="relative inline-block text-left" ref={dropdownRef}>
      <div onClick={() => setIsOpen(!isOpen)}>
        {trigger}
      </div>
      {isOpen && (
        <div
          ref={menuRef}
          className="absolute w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50"
          style={{ top: '100%', left: '0px', marginTop: '8px' }} // Default positioning
          role="menu"
          aria-orientation="vertical"
          aria-labelledby="menu-button"
          onClick={() => setIsOpen(false)} // Close dropdown on item click
        >
            {children}
        </div>
      )}
    </div>
  );
};

export default Dropdown;