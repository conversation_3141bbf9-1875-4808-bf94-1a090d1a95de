

import React from 'react';

interface TableProps<T> {
  columns: { key: keyof T | 'actions'; header: string; render?: (item: T) => React.ReactNode }[];
  data: T[];
}

const Table = <T extends { _id: string },>(
  { columns, data }: TableProps<T>
) => {
  return (
    <div className="overflow-x-auto table-scrollbar-hidden">
      <div className="inline-block min-w-full align-middle">
        <div className="overflow-visible">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {columns.map((col) => (
                  <th
                    key={String(col.key)}
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-bold text-gray-600 uppercase tracking-wider"
                  >
                    {col.header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {data.length === 0 ? (
                <tr>
                  <td colSpan={columns.length} className="px-6 py-12 text-center text-gray-500">
                    No data available.
                  </td>
                </tr>
              ) : (
                data.map((item) => (
                  <tr key={item._id} className="hover:bg-gray-50 transition-colors">
                    {columns.map((col) => (
                      <td key={String(col.key)} className="px-6 py-4 whitespace-nowrap text-sm text-gray-800">
                        {col.render ? col.render(item) : String(item[col.key as keyof T] ?? '')}
                      </td>
                    ))}
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default Table;