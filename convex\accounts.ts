import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Get all accounts for a workspace
export const getAccounts = query({
  args: { workspaceId: v.id("workspaces") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("accounts")
      .filter((q) => q.eq(q.field("workspaceId"), args.workspaceId))
      .collect();
  },
});

// Get a specific account
export const getAccount = query({
  args: { accountId: v.id("accounts") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.accountId);
  },
});

// Create a new account
export const createAccount = mutation({
  args: {
    workspaceId: v.id("workspaces"),
    code: v.string(),
    name: v.string(),
    type: v.string(),
    balance: v.number(),
  },
  handler: async (ctx, args) => {
    // Check if account code already exists
    const existingAccount = await ctx.db
      .query("accounts")
      .filter((q) => 
        q.and(
          q.eq(q.field("workspaceId"), args.workspaceId),
          q.eq(q.field("code"), args.code)
        )
      )
      .first();

    if (existingAccount) {
      throw new Error("Account code already exists");
    }

    return await ctx.db.insert("accounts", {
      workspaceId: args.workspaceId,
      code: args.code,
      name: args.name,
      type: args.type,
      balance: args.balance,
    });
  },
});

// Update an account
export const updateAccount = mutation({
  args: {
    accountId: v.id("accounts"),
    code: v.string(),
    name: v.string(),
    type: v.string(),
  },
  handler: async (ctx, args) => {
    const account = await ctx.db.get(args.accountId);
    if (!account) {
      throw new Error("Account not found");
    }

    // Check if new code conflicts with existing accounts
    if (account.code !== args.code) {
      const existingAccount = await ctx.db
        .query("accounts")
        .filter((q) => 
          q.and(
            q.eq(q.field("workspaceId"), account.workspaceId),
            q.eq(q.field("code"), args.code)
          )
        )
        .first();

      if (existingAccount) {
        throw new Error("Account code already exists");
      }
    }

    await ctx.db.patch(args.accountId, {
      code: args.code,
      name: args.name,
      type: args.type,
    });
  },
});

// Update account balance
export const updateAccountBalance = mutation({
  args: {
    accountId: v.id("accounts"),
    amount: v.number(),
  },
  handler: async (ctx, args) => {
    const account = await ctx.db.get(args.accountId);
    if (!account) {
      throw new Error("Account not found");
    }

    await ctx.db.patch(args.accountId, {
      balance: account.balance + args.amount,
    });
  },
});

// Delete an account
export const deleteAccount = mutation({
  args: { accountId: v.id("accounts") },
  handler: async (ctx, args) => {
    // Check if account is used in any journal entries
    const journalEntries = await ctx.db
      .query("journalEntries")
      .collect();

    const isUsed = journalEntries.some(entry => 
      entry.debits.some(debit => debit.accountId === args.accountId) ||
      entry.credits.some(credit => credit.accountId === args.accountId)
    );

    if (isUsed) {
      throw new Error("Cannot delete account that is used in journal entries");
    }

    await ctx.db.delete(args.accountId);
  },
});
