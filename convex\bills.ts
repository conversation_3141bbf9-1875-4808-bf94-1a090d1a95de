import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Get all bills for a workspace
export const getBills = query({
  args: { workspaceId: v.id("workspaces") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("bills")
      .filter((q) => q.eq(q.field("workspaceId"), args.workspaceId))
      .collect();
  },
});

// Get a specific bill
export const getBill = query({
  args: { billId: v.id("bills") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.billId);
  },
});

// Create a new bill
export const createBill = mutation({
  args: {
    workspaceId: v.id("workspaces"),
    supplierId: v.id("suppliers"),
    billNumber: v.string(),
    issueDate: v.string(),
    dueDate: v.string(),
    amount: v.number(),
    status: v.string(),
    items: v.array(v.object({
      productId: v.optional(v.id("products")),
      description: v.string(),
      quantity: v.number(),
      price: v.number(),
      total: v.number(),
    })),
    taxId: v.optional(v.id("taxRates")),
    taxRate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("bills", {
      workspaceId: args.workspaceId,
      supplierId: args.supplierId,
      billNumber: args.billNumber,
      issueDate: args.issueDate,
      dueDate: args.dueDate,
      amount: args.amount,
      status: args.status,
      items: args.items,
      taxId: args.taxId,
      taxRate: args.taxRate,
    });
  },
});

// Update a bill
export const updateBill = mutation({
  args: {
    billId: v.id("bills"),
    supplierId: v.id("suppliers"),
    billNumber: v.string(),
    issueDate: v.string(),
    dueDate: v.string(),
    amount: v.number(),
    status: v.string(),
    items: v.array(v.object({
      productId: v.optional(v.id("products")),
      description: v.string(),
      quantity: v.number(),
      price: v.number(),
      total: v.number(),
    })),
    taxId: v.optional(v.id("taxRates")),
    taxRate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.billId, {
      supplierId: args.supplierId,
      billNumber: args.billNumber,
      issueDate: args.issueDate,
      dueDate: args.dueDate,
      amount: args.amount,
      status: args.status,
      items: args.items,
      taxId: args.taxId,
      taxRate: args.taxRate,
    });
  },
});

// Update bill status
export const updateBillStatus = mutation({
  args: {
    billId: v.id("bills"),
    status: v.string(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.billId, {
      status: args.status,
    });
  },
});

// Delete a bill
export const deleteBill = mutation({
  args: { billId: v.id("bills") },
  handler: async (ctx, args) => {
    await ctx.db.delete(args.billId);
  },
});