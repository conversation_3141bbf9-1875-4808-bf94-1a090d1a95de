import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Get all clients for a workspace
export const getClients = query({
  args: { workspaceId: v.id("workspaces") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("clients")
      .filter((q) => q.eq(q.field("workspaceId"), args.workspaceId))
      .collect();
  },
});

// Get a specific client
export const getClient = query({
  args: { clientId: v.id("clients") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.clientId);
  },
});

// Create a new client
export const createClient = mutation({
  args: {
    workspaceId: v.id("workspaces"),
    name: v.string(),
    email: v.optional(v.string()),
    phone: v.optional(v.string()),
    address: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("clients", {
      workspaceId: args.workspaceId,
      name: args.name,
      email: args.email,
      phone: args.phone,
      address: args.address,
    });
  },
});

// Update a client
export const updateClient = mutation({
  args: {
    clientId: v.id("clients"),
    name: v.string(),
    email: v.optional(v.string()),
    phone: v.optional(v.string()),
    address: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.clientId, {
      name: args.name,
      email: args.email,
      phone: args.phone,
      address: args.address,
    });
  },
});

// Delete a client
export const deleteClient = mutation({
  args: { clientId: v.id("clients") },
  handler: async (ctx, args) => {
    // Check if client has any invoices or quotes
    const invoices = await ctx.db
      .query("invoices")
      .filter((q) => q.eq(q.field("clientId"), args.clientId))
      .collect();
    
    const quotes = await ctx.db
      .query("quotes")
      .filter((q) => q.eq(q.field("clientId"), args.clientId))
      .collect();
    
    if (invoices.length > 0 || quotes.length > 0) {
      throw new Error("Cannot delete client with existing invoices or quotes");
    }
    
    await ctx.db.delete(args.clientId);
  },
});
