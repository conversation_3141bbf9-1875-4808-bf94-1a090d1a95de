import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Get company profile for a workspace
export const getCompanyProfile = query({
  args: { workspaceId: v.id("workspaces") },
  handler: async (ctx, args) => {
    const profile = await ctx.db
      .query("companyProfiles")
      .filter((q) => q.eq(q.field("workspaceId"), args.workspaceId))
      .first();
    return profile;
  },
});

// Create a company profile
export const createCompanyProfile = mutation({
  args: {
    workspaceId: v.id("workspaces"),
    name: v.string(),
    address: v.optional(v.string()),
    email: v.optional(v.string()),
    phone: v.optional(v.string()),
    logoUrl: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("companyProfiles", {
      workspaceId: args.workspaceId,
      name: args.name,
      address: args.address,
      email: args.email,
      phone: args.phone,
      logoUrl: args.logoUrl,
    });
  },
});

// Update a company profile
export const updateCompanyProfile = mutation({
  args: {
    profileId: v.id("companyProfiles"),
    name: v.string(),
    address: v.optional(v.string()),
    email: v.optional(v.string()),
    phone: v.optional(v.string()),
    logoUrl: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.profileId, {
      name: args.name,
      address: args.address,
      email: args.email,
      phone: args.phone,
      logoUrl: args.logoUrl,
    });
  },
});

// Delete a company profile
export const deleteCompanyProfile = mutation({
  args: { profileId: v.id("companyProfiles") },
  handler: async (ctx, args) => {
    await ctx.db.delete(args.profileId);
  },
});
