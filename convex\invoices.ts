import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Get all invoices for a workspace
export const getInvoices = query({
  args: { workspaceId: v.id("workspaces") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("invoices")
      .filter((q) => q.eq(q.field("workspaceId"), args.workspaceId))
      .collect();
  },
});

// Get a specific invoice
export const getInvoice = query({
  args: { invoiceId: v.id("invoices") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.invoiceId);
  },
});

// Create a new invoice
export const createInvoice = mutation({
  args: {
    workspaceId: v.id("workspaces"),
    clientId: v.id("clients"),
    invoiceNumber: v.optional(v.string()),
    issueDate: v.string(),
    dueDate: v.string(),
    amount: v.number(),
    status: v.string(),
    notes: v.optional(v.string()),
    discountType: v.optional(v.string()),
    discountValue: v.optional(v.number()),
    items: v.array(v.object({
      productId: v.optional(v.id("products")),
      description: v.string(),
      quantity: v.number(),
      unitPrice: v.number(),
      total: v.number(),
    })),
    taxId: v.optional(v.id("taxRates")),
    taxRate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("invoices", {
      workspaceId: args.workspaceId,
      clientId: args.clientId,
      invoiceNumber: args.invoiceNumber,
      issueDate: args.issueDate,
      dueDate: args.dueDate,
      amount: args.amount,
      status: args.status,
      notes: args.notes,
      discountType: args.discountType,
      discountValue: args.discountValue,
      items: args.items,
      taxId: args.taxId,
      taxRate: args.taxRate,
    });
  },
});

// Update an invoice
export const updateInvoice = mutation({
  args: {
    invoiceId: v.id("invoices"),
    clientId: v.id("clients"),
    invoiceNumber: v.optional(v.string()),
    issueDate: v.string(),
    dueDate: v.string(),
    amount: v.number(),
    status: v.string(),
    notes: v.optional(v.string()),
    discountType: v.optional(v.string()),
    discountValue: v.optional(v.number()),
    items: v.array(v.object({
      productId: v.optional(v.id("products")),
      description: v.string(),
      quantity: v.number(),
      unitPrice: v.number(),
      total: v.number(),
    })),
    taxId: v.optional(v.id("taxRates")),
    taxRate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.invoiceId, {
      clientId: args.clientId,
      invoiceNumber: args.invoiceNumber,
      issueDate: args.issueDate,
      dueDate: args.dueDate,
      amount: args.amount,
      status: args.status,
      notes: args.notes,
      discountType: args.discountType,
      discountValue: args.discountValue,
      items: args.items,
      taxId: args.taxId,
      taxRate: args.taxRate,
    });
  },
});

// Update invoice status
export const updateInvoiceStatus = mutation({
  args: {
    invoiceId: v.id("invoices"),
    status: v.string(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.invoiceId, {
      status: args.status,
    });
  },
});

// Delete an invoice
export const deleteInvoice = mutation({
  args: { invoiceId: v.id("invoices") },
  handler: async (ctx, args) => {
    await ctx.db.delete(args.invoiceId);
  },
});