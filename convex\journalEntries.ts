import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Get all journal entries for a workspace
export const getJournalEntries = query({
  args: { workspaceId: v.id("workspaces") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("journalEntries")
      .filter((q) => q.eq(q.field("workspaceId"), args.workspaceId))
      .collect();
  },
});

// Get a specific journal entry
export const getJournalEntry = query({
  args: { entryId: v.id("journalEntries") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.entryId);
  },
});

// Create a new journal entry
export const createJournalEntry = mutation({
  args: {
    workspaceId: v.id("workspaces"),
    date: v.string(),
    description: v.string(),
    debits: v.array(v.object({
      accountId: v.id("accounts"),
      accountName: v.string(),
      amount: v.number(),
    })),
    credits: v.array(v.object({
      accountId: v.id("accounts"),
      accountName: v.string(),
      amount: v.number(),
    })),
    origin: v.optional(v.object({
      type: v.string(),
      id: v.string(),
    })),
  },
  handler: async (ctx, args) => {
    // Validate that debits equal credits
    const totalDebits = args.debits.reduce((sum, debit) => sum + debit.amount, 0);
    const totalCredits = args.credits.reduce((sum, credit) => sum + credit.amount, 0);

    if (Math.abs(totalDebits - totalCredits) > 0.01) {
      throw new Error("Debits must equal credits");
    }

    // Create the journal entry
    const entryId = await ctx.db.insert("journalEntries", {
      workspaceId: args.workspaceId,
      date: args.date,
      description: args.description,
      debits: args.debits,
      credits: args.credits,
      origin: args.origin,
    });

    // Update account balances
    for (const debit of args.debits) {
      const account = await ctx.db.get(debit.accountId);
      if (account) {
        await ctx.db.patch(debit.accountId, {
          balance: account.balance + debit.amount,
        });
      }
    }

    for (const credit of args.credits) {
      const account = await ctx.db.get(credit.accountId);
      if (account) {
        await ctx.db.patch(credit.accountId, {
          balance: account.balance - credit.amount,
        });
      }
    }

    return entryId;
  },
});

// Delete a journal entry
export const deleteJournalEntry = mutation({
  args: { entryId: v.id("journalEntries") },
  handler: async (ctx, args) => {
    const entry = await ctx.db.get(args.entryId);
    if (!entry) {
      throw new Error("Journal entry not found");
    }

    // Reverse the account balance changes
    for (const debit of entry.debits) {
      const account = await ctx.db.get(debit.accountId);
      if (account) {
        await ctx.db.patch(debit.accountId, {
          balance: account.balance - debit.amount,
        });
      }
    }

    for (const credit of entry.credits) {
      const account = await ctx.db.get(credit.accountId);
      if (account) {
        await ctx.db.patch(credit.accountId, {
          balance: account.balance + credit.amount,
        });
      }
    }

    await ctx.db.delete(args.entryId);
  },
});