import { v } from "convex/values";
import { mutation } from "./_generated/server";

// Migration to update products schema from old format to new format
export const migrateProductsSchema = mutation({
  args: {},
  handler: async (ctx, args) => {
    // Get all products
    const products = await ctx.db.query("products").collect();
    
    let migratedCount = 0;
    let skippedCount = 0;
    
    for (const product of products) {
      // Check if product has old schema fields
      const productAny = product as any;
      const hasOldFields = 'price' in productAny || 'stockQuantity' in productAny || 'description' in productAny;
      const hasNewFields = 'unitPrice' in productAny && 'stock' in productAny;

      if (hasOldFields && !hasNewFields) {
        // Migrate from old schema to new schema
        const updatedProduct: any = {
          name: productAny.name,
          category: productAny.category || "General",
          sku: productAny.sku || `SKU-${productAny._id.slice(-6)}`,
          stock: productAny.stockQuantity || 0,
          unitPrice: productAny.price || 0,
        };

        // Remove old fields and update with new schema
        await ctx.db.patch(productAny._id, updatedProduct);
        migratedCount++;
      } else if (hasNewFields) {
        // Product already has new schema
        skippedCount++;
      } else {
        // Product has neither old nor new fields properly - fix it
        const updatedProduct: any = {
          name: productAny.name || "Unknown Product",
          category: productAny.category || "General",
          sku: productAny.sku || `SKU-${productAny._id.slice(-6)}`,
          stock: 0,
          unitPrice: 0,
        };

        await ctx.db.patch(productAny._id, updatedProduct);
        migratedCount++;
      }
    }
    
    return {
      message: "Product schema migration completed",
      totalProducts: products.length,
      migratedCount,
      skippedCount,
    };
  },
});

// Clear all products (useful for testing)
export const clearAllProducts = mutation({
  args: { 
    workspaceId: v.optional(v.id("workspaces")),
    confirmDelete: v.boolean()
  },
  handler: async (ctx, args) => {
    if (!args.confirmDelete) {
      throw new Error("Must confirm deletion by setting confirmDelete to true");
    }
    
    let query = ctx.db.query("products");
    
    if (args.workspaceId) {
      const products = await query
        .filter((q) => q.eq(q.field("workspaceId"), args.workspaceId))
        .collect();
      
      for (const product of products) {
        await ctx.db.delete(product._id);
      }
      
      return {
        message: `Deleted ${products.length} products from workspace ${args.workspaceId}`,
        deletedCount: products.length,
      };
    } else {
      const products = await query.collect();
      
      for (const product of products) {
        await ctx.db.delete(product._id);
      }
      
      return {
        message: `Deleted all ${products.length} products`,
        deletedCount: products.length,
      };
    }
  },
});

// Reset products for a workspace with sample data
export const resetWorkspaceProducts = mutation({
  args: { 
    workspaceId: v.id("workspaces"),
    confirmReset: v.boolean()
  },
  handler: async (ctx, args) => {
    if (!args.confirmReset) {
      throw new Error("Must confirm reset by setting confirmReset to true");
    }
    
    // Delete existing products for this workspace
    const existingProducts = await ctx.db
      .query("products")
      .filter((q) => q.eq(q.field("workspaceId"), args.workspaceId))
      .collect();
    
    for (const product of existingProducts) {
      await ctx.db.delete(product._id);
    }
    
    // Create sample products with new schema
    const sampleProducts = [
      {
        name: "Web Development Service",
        category: "Services",
        sku: "WEB-DEV-001",
        stock: 9999,
        unitPrice: 100,
      },
      {
        name: "UX Design Package",
        category: "Services",
        sku: "UX-PKG-001",
        stock: 9999,
        unitPrice: 2500,
      },
      {
        name: "Standard Keyboard",
        category: "Hardware",
        sku: "KB-STD-001",
        stock: 50,
        unitPrice: 40,
      },
      {
        name: "Wireless Mouse",
        category: "Hardware",
        sku: "HW-MS-001",
        stock: 15,
        unitPrice: 25,
      },
      {
        name: "Mobile App Development",
        category: "Services",
        sku: "SVC-MOB-001",
        stock: 9999,
        unitPrice: 150,
      },
      {
        name: "SEO Consultation",
        category: "Services",
        sku: "SVC-SEO-001",
        stock: 9999,
        unitPrice: 75,
      },
    ];
    
    const createdProducts = [];
    for (const productData of sampleProducts) {
      const productId = await ctx.db.insert("products", {
        workspaceId: args.workspaceId,
        ...productData,
      });
      createdProducts.push(productId);
    }
    
    return {
      message: `Reset products for workspace. Deleted ${existingProducts.length} old products and created ${createdProducts.length} new products.`,
      deletedCount: existingProducts.length,
      createdCount: createdProducts.length,
      createdProductIds: createdProducts,
    };
  },
});
