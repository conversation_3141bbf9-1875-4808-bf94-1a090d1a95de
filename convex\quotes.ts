import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Get all quotes for a workspace
export const getQuotes = query({
  args: { workspaceId: v.id("workspaces") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("quotes")
      .filter((q) => q.eq(q.field("workspaceId"), args.workspaceId))
      .collect();
  },
});

// Get a specific quote
export const getQuote = query({
  args: { quoteId: v.id("quotes") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.quoteId);
  },
});

// Create a new quote
export const createQuote = mutation({
  args: {
    workspaceId: v.id("workspaces"),
    clientId: v.id("clients"),
    issueDate: v.string(),
    expiryDate: v.string(),
    amount: v.number(),
    status: v.string(),
    items: v.array(v.object({
      productId: v.optional(v.id("products")),
      description: v.string(),
      quantity: v.number(),
      unitPrice: v.number(),
      total: v.number(),
    })),
    taxId: v.optional(v.id("taxRates")),
    taxRate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("quotes", {
      workspaceId: args.workspaceId,
      clientId: args.clientId,
      issueDate: args.issueDate,
      expiryDate: args.expiryDate,
      amount: args.amount,
      status: args.status,
      items: args.items,
      taxId: args.taxId,
      taxRate: args.taxRate,
    });
  },
});

// Update a quote
export const updateQuote = mutation({
  args: {
    quoteId: v.id("quotes"),
    clientId: v.id("clients"),
    issueDate: v.string(),
    expiryDate: v.string(),
    amount: v.number(),
    status: v.string(),
    items: v.array(v.object({
      productId: v.optional(v.id("products")),
      description: v.string(),
      quantity: v.number(),
      unitPrice: v.number(),
      total: v.number(),
    })),
    taxId: v.optional(v.id("taxRates")),
    taxRate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.quoteId, {
      clientId: args.clientId,
      issueDate: args.issueDate,
      expiryDate: args.expiryDate,
      amount: args.amount,
      status: args.status,
      items: args.items,
      taxId: args.taxId,
      taxRate: args.taxRate,
    });
  },
});

// Update quote status
export const updateQuoteStatus = mutation({
  args: {
    quoteId: v.id("quotes"),
    status: v.string(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.quoteId, {
      status: args.status,
    });
  },
});

// Convert quote to invoice
export const convertToInvoice = mutation({
  args: {
    quoteId: v.id("quotes"),
    invoiceNumber: v.string(),
    dueDate: v.string(),
  },
  handler: async (ctx, args) => {
    const quote = await ctx.db.get(args.quoteId);
    if (!quote) {
      throw new Error("Quote not found");
    }

    // Create invoice from quote
    const invoiceId = await ctx.db.insert("invoices", {
      workspaceId: quote.workspaceId,
      clientId: quote.clientId,
      invoiceNumber: args.invoiceNumber,
      issueDate: new Date().toISOString().split('T')[0],
      dueDate: args.dueDate,
      amount: quote.amount,
      status: "Draft",
      items: quote.items,
      taxId: quote.taxId,
      taxRate: quote.taxRate,
    });

    // Update quote status to accepted
    await ctx.db.patch(args.quoteId, {
      status: "Accepted",
    });

    return invoiceId;
  },
});

// Delete a quote
export const deleteQuote = mutation({
  args: { quoteId: v.id("quotes") },
  handler: async (ctx, args) => {
    await ctx.db.delete(args.quoteId);
  },
});
