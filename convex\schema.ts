import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  users: defineTable({
    email: v.string(),
    name: v.string(),
    passwordHash: v.string(),
  }).index("by_email", ["email"]),

  workspaces: defineTable({
    name: v.string(),
    ownerId: v.id("users"),
  }),

  teamMembers: defineTable({
    workspaceId: v.id("workspaces"),
    userId: v.id("users"),
    name: v.string(),
    email: v.string(),
    role: v.string(),
    status: v.string(),
  }),

  companyProfiles: defineTable({
    workspaceId: v.id("workspaces"),
    name: v.string(),
    address: v.optional(v.string()),
    email: v.optional(v.string()),
    phone: v.optional(v.string()),
    logoUrl: v.optional(v.string()),
  }),

  clients: defineTable({
    workspaceId: v.id("workspaces"),
    name: v.string(),
    email: v.optional(v.string()),
    phone: v.optional(v.string()),
    address: v.optional(v.string()),
  }),

  suppliers: defineTable({
    workspaceId: v.id("workspaces"),
    name: v.string(),
    email: v.optional(v.string()),
    phone: v.optional(v.string()),
    address: v.optional(v.string()),
  }),

  products: defineTable({
    workspaceId: v.id("workspaces"),
    name: v.string(),
    category: v.string(),
    sku: v.string(),
    stock: v.number(),
    unitPrice: v.number(),
  }),

  taxRates: defineTable({
    workspaceId: v.id("workspaces"),
    name: v.string(),
    rate: v.number(),
    isDefault: v.boolean(),
  }),

  accounts: defineTable({
    workspaceId: v.id("workspaces"),
    code: v.string(),
    name: v.string(),
    type: v.string(),
    balance: v.number(),
  }),

  journalEntries: defineTable({
    workspaceId: v.id("workspaces"),
    date: v.string(),
    description: v.string(),
    debits: v.array(v.object({
      accountId: v.id("accounts"),
      accountName: v.string(),
      amount: v.number(),
    })),
    credits: v.array(v.object({
      accountId: v.id("accounts"),
      accountName: v.string(),
      amount: v.number(),
    })),
    origin: v.optional(v.object({
      type: v.string(),
      id: v.string(),
    })),
  }),

  invoices: defineTable({
    workspaceId: v.id("workspaces"),
    clientId: v.id("clients"),
    invoiceNumber: v.optional(v.string()),
    issueDate: v.string(),
    dueDate: v.string(),
    amount: v.number(),
    status: v.string(),
    notes: v.optional(v.string()),
    discountType: v.optional(v.string()),
    discountValue: v.optional(v.number()),
    items: v.array(v.object({
      productId: v.optional(v.id("products")),
      description: v.string(),
      quantity: v.number(),
      unitPrice: v.number(),
      total: v.number(),
    })),
    taxId: v.optional(v.id("taxRates")),
    taxRate: v.optional(v.number()),
  }),

  bills: defineTable({
    workspaceId: v.id("workspaces"),
    supplierId: v.id("suppliers"),
    billNumber: v.string(),
    issueDate: v.string(),
    dueDate: v.string(),
    amount: v.number(),
    status: v.string(),
    items: v.array(v.object({
      productId: v.optional(v.id("products")),
      description: v.string(),
      quantity: v.number(),
      price: v.number(),
      total: v.number(),
    })),
    taxId: v.optional(v.id("taxRates")),
    taxRate: v.optional(v.number()),
  }),

  quotes: defineTable({
    workspaceId: v.id("workspaces"),
    clientId: v.id("clients"),
    issueDate: v.string(),
    expiryDate: v.string(),
    amount: v.number(),
    status: v.string(),
    items: v.array(v.object({
      productId: v.optional(v.id("products")),
      description: v.string(),
      quantity: v.number(),
      unitPrice: v.number(),
      total: v.number(),
    })),
    taxId: v.optional(v.id("taxRates")),
    taxRate: v.optional(v.number()),
  }),

  purchaseOrders: defineTable({
    workspaceId: v.id("workspaces"),
    supplierId: v.id("suppliers"),
    orderNumber: v.string(),
    orderDate: v.string(),
    expectedDate: v.string(),
    amount: v.number(),
    status: v.string(),
    items: v.array(v.object({
      productId: v.optional(v.id("products")),
      description: v.string(),
      quantity: v.number(),
      price: v.number(),
      total: v.number(),
    })),
  }),
});