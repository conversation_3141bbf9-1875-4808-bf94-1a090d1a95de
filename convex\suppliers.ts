import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Get all suppliers for a workspace
export const getSuppliers = query({
  args: { workspaceId: v.id("workspaces") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("suppliers")
      .filter((q) => q.eq(q.field("workspaceId"), args.workspaceId))
      .collect();
  },
});

// Get a specific supplier
export const getSupplier = query({
  args: { supplierId: v.id("suppliers") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.supplierId);
  },
});

// Create a new supplier
export const createSupplier = mutation({
  args: {
    workspaceId: v.id("workspaces"),
    name: v.string(),
    email: v.optional(v.string()),
    phone: v.optional(v.string()),
    address: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("suppliers", {
      workspaceId: args.workspaceId,
      name: args.name,
      email: args.email,
      phone: args.phone,
      address: args.address,
    });
  },
});

// Update a supplier
export const updateSupplier = mutation({
  args: {
    supplierId: v.id("suppliers"),
    name: v.string(),
    email: v.optional(v.string()),
    phone: v.optional(v.string()),
    address: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.supplierId, {
      name: args.name,
      email: args.email,
      phone: args.phone,
      address: args.address,
    });
  },
});

// Delete a supplier
export const deleteSupplier = mutation({
  args: { supplierId: v.id("suppliers") },
  handler: async (ctx, args) => {
    // Check if supplier has any bills or purchase orders
    const bills = await ctx.db
      .query("bills")
      .filter((q) => q.eq(q.field("supplierId"), args.supplierId))
      .collect();
    
    const purchaseOrders = await ctx.db
      .query("purchaseOrders")
      .filter((q) => q.eq(q.field("supplierId"), args.supplierId))
      .collect();
    
    if (bills.length > 0 || purchaseOrders.length > 0) {
      throw new Error("Cannot delete supplier with existing bills or purchase orders");
    }
    
    await ctx.db.delete(args.supplierId);
  },
});
