import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Get all tax rates for a workspace
export const getTaxRates = query({
  args: { workspaceId: v.id("workspaces") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("taxRates")
      .filter((q) => q.eq(q.field("workspaceId"), args.workspaceId))
      .collect();
  },
});

// Get a specific tax rate
export const getTaxRate = query({
  args: { taxRateId: v.id("taxRates") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.taxRateId);
  },
});

// Create a tax rate
export const createTaxRate = mutation({
  args: {
    workspaceId: v.id("workspaces"),
    name: v.string(),
    rate: v.number(),
    isDefault: v.boolean(),
  },
  handler: async (ctx, args) => {
    // If this is set as default, unset all other defaults
    if (args.isDefault) {
      const existingTaxRates = await ctx.db
        .query("taxRates")
        .filter((q) => q.eq(q.field("workspaceId"), args.workspaceId))
        .collect();
      
      for (const taxRate of existingTaxRates) {
        if (taxRate.isDefault) {
          await ctx.db.patch(taxRate._id, { isDefault: false });
        }
      }
    }

    return await ctx.db.insert("taxRates", {
      workspaceId: args.workspaceId,
      name: args.name,
      rate: args.rate,
      isDefault: args.isDefault,
    });
  },
});

// Update a tax rate
export const updateTaxRate = mutation({
  args: {
    taxRateId: v.id("taxRates"),
    name: v.string(),
    rate: v.number(),
    isDefault: v.boolean(),
  },
  handler: async (ctx, args) => {
    const taxRate = await ctx.db.get(args.taxRateId);
    if (!taxRate) {
      throw new Error("Tax rate not found");
    }

    // If this is set as default, unset all other defaults in the same workspace
    if (args.isDefault) {
      const existingTaxRates = await ctx.db
        .query("taxRates")
        .filter((q) => q.eq(q.field("workspaceId"), taxRate.workspaceId))
        .collect();
      
      for (const otherTaxRate of existingTaxRates) {
        if (otherTaxRate._id !== args.taxRateId && otherTaxRate.isDefault) {
          await ctx.db.patch(otherTaxRate._id, { isDefault: false });
        }
      }
    }

    await ctx.db.patch(args.taxRateId, {
      name: args.name,
      rate: args.rate,
      isDefault: args.isDefault,
    });
  },
});

// Delete a tax rate
export const deleteTaxRate = mutation({
  args: { taxRateId: v.id("taxRates") },
  handler: async (ctx, args) => {
    await ctx.db.delete(args.taxRateId);
  },
});
