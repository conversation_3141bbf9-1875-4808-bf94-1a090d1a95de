import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

export const getCurrentUser = query({
  args: { userId: v.optional(v.id("users")) },
  handler: async (ctx, args) => {
    if (!args.userId) {
      return null;
    }
    return await ctx.db.get(args.userId);
  },
});

export const createUserProfile = mutation({
  args: {
    userId: v.id("users"),
    businessName: v.optional(v.string()),
    phoneNumber: v.optional(v.string()),
    physicalAddress: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.userId);
    if (!user) {
      throw new Error("User not found");
    }

    // Create a default workspace for the user
    const workspaceId = await ctx.db.insert("workspaces", {
      name: args.businessName || `${user.name}'s Workspace`,
      ownerId: args.userId,
    });

    // Add user as team member to their own workspace
    await ctx.db.insert("teamMembers", {
      workspaceId,
      userId: args.userId,
      name: user.name,
      email: user.email,
      role: "Admin",
      status: "Active",
    });

    return { userId: args.userId, workspaceId };
  },
});

export const getUserWorkspaces = query({
  args: { userId: v.optional(v.id("users")) },
  handler: async (ctx, args) => {
    if (!args.userId) {
      return [];
    }

    const teamMemberships = await ctx.db
      .query("teamMembers")
      .filter((q) => q.eq(q.field("userId"), args.userId))
      .collect();

    const workspaces = await Promise.all(
      teamMemberships.map(async (membership) => {
        const workspace = await ctx.db.get(membership.workspaceId);
        return workspace;
      })
    );

    return workspaces.filter(Boolean);
  },
});