import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Get all workspaces for a user
export const getUserWorkspaces = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("workspaces")
      .filter((q) => q.eq(q.field("ownerId"), args.userId))
      .collect();
  },
});

// Get a specific workspace
export const getWorkspace = query({
  args: { workspaceId: v.id("workspaces") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.workspaceId);
  },
});

// Create a new workspace
export const createWorkspace = mutation({
  args: {
    name: v.string(),
    ownerId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const workspaceId = await ctx.db.insert("workspaces", {
      name: args.name,
      ownerId: args.ownerId,
    });
    
    // Create default company profile
    await ctx.db.insert("companyProfiles", {
      workspaceId,
      name: args.name,
    });
    
    // Create default chart of accounts
    const defaultAccounts = [
      { code: "1010", name: "Business Checking", type: "Asset", balance: 0 },
      { code: "1200", name: "Accounts Receivable", type: "Asset", balance: 0 },
      { code: "2010", name: "Accounts Payable", type: "Liability", balance: 0 },
      { code: "2200", name: "Tax Payable", type: "Liability", balance: 0 },
      { code: "3000", name: "Owner's Equity", type: "Equity", balance: 0 },
      { code: "4000", name: "Service Revenue", type: "Revenue", balance: 0 },
      { code: "5000", name: "Software Expense", type: "Expense", balance: 0 },
      { code: "5010", name: "Miscellaneous Expense", type: "Expense", balance: 0 },
      { code: "5020", name: "Rent Expense", type: "Expense", balance: 0 },
    ];
    
    for (const account of defaultAccounts) {
      await ctx.db.insert("accounts", {
        workspaceId,
        ...account,
      });
    }
    
    // Create default tax rate
    await ctx.db.insert("taxRates", {
      workspaceId,
      name: "Standard Tax",
      rate: 0.1,
      isDefault: true,
    });
    
    return workspaceId;
  },
});

// Update workspace
export const updateWorkspace = mutation({
  args: {
    workspaceId: v.id("workspaces"),
    name: v.string(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.workspaceId, {
      name: args.name,
    });
  },
});

// Delete workspace
export const deleteWorkspace = mutation({
  args: { workspaceId: v.id("workspaces") },
  handler: async (ctx, args) => {
    // Delete all related data first
    const tables = [
      "companyProfiles",
      "clients", 
      "suppliers",
      "products",
      "taxRates",
      "accounts",
      "journalEntries",
      "invoices",
      "bills",
      "quotes",
      "purchaseOrders",
      "teamMembers"
    ];
    
    for (const table of tables) {
      const items = await ctx.db
        .query(table as any)
        .filter((q) => q.eq(q.field("workspaceId"), args.workspaceId))
        .collect();
      
      for (const item of items) {
        await ctx.db.delete(item._id);
      }
    }
    
    // Finally delete the workspace
    await ctx.db.delete(args.workspaceId);
  },
});
