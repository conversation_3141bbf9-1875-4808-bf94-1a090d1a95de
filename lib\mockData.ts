import { User, Workspace, Client, Invoice, Bill, Product, Quote, Account, JournalEntry, TeamMember, Supplier, PurchaseOrder, TaxRate, CompanyProfile, UserRole, TeamMemberStatus, InvoiceStatus, BillStatus, QuoteStatus, PurchaseOrderStatus, AccountType } from '../types';

const USER_ID = 'user-1';
const WORKSPACE_ID = 'ws-1';
const now = Date.now();

const createId = (prefix: string) => `${prefix}-${Math.random().toString(36).substr(2, 9)}`;

export let mockData = {
  users: [
    { _id: USER_ID, _creationTime: now, name: '<PERSON>', email: '<EMAIL>', tokenIdentifier: 'token-123' }
  ] as User[],
  workspaces: [
    { _id: WORKSPACE_ID, _creationTime: now, name: 'FinHog Corp', ownerId: USER_ID },
    { _id: 'ws-2', _creationTime: now, name: 'Personal', ownerId: USER_ID },
  ] as Workspace[],
  companyProfiles: [
      { _id: createId('cp'), _creationTime: now, workspaceId: WORKSPACE_ID, name: 'FinHog Corp', address: '123 Tech Lane\nInnovation City, 12345', email: '<EMAIL>', phone: '************', logoUrl: 'https://storage.googleapis.com/proudcity/mebanenc/uploads/2021/03/placeholder-image.png' }
  ] as CompanyProfile[],
  clients: [
      { _id: 'client-1', _creationTime: now - *********30, workspaceId: WORKSPACE_ID, name: 'Innovate LLC', email: '<EMAIL>', phone: '555-0101', address: '456 Progress Ave', createdAt: new Date(now - *********30).toISOString().split('T')[0] },
      { _id: 'client-2', _creationTime: now - *********5, workspaceId: WORKSPACE_ID, name: 'Data Systems', email: '<EMAIL>', phone: '555-0102', address: '789 Info Highway', createdAt: new Date(now - *********5).toISOString().split('T')[0] },
  ] as Client[],
  suppliers: [
      { _id: 'sup-1', _creationTime: now, workspaceId: WORKSPACE_ID, name: 'Cloud Services Inc.', email: '<EMAIL>', category: 'Software', address: '1 Cloud Plaza', phone: '555-0201' },
      { _id: 'sup-2', _creationTime: now, workspaceId: WORKSPACE_ID, name: 'Office Supplies Co.', email: '<EMAIL>', category: 'Supplies', address: '200 Paper St', phone: '555-0202' },
  ] as Supplier[],
  products: [
      { _id: 'prod-1', _creationTime: now, workspaceId: WORKSPACE_ID, name: 'Web Dev Services', category: 'Services', sku: 'SVC-WEB-01', stock: 9999, unitPrice: 100 },
      { _id: 'prod-2', _creationTime: now, workspaceId: WORKSPACE_ID, name: 'UX Design Package', category: 'Services', sku: 'SVC-UX-01', stock: 9999, unitPrice: 2500 },
      { _id: 'prod-3', _creationTime: now, workspaceId: WORKSPACE_ID, name: 'Standard Keyboard', category: 'Hardware', sku: 'HW-KB-01', stock: 8, unitPrice: 45 },
      { _id: 'prod-4', _creationTime: now, workspaceId: WORKSPACE_ID, name: 'Wireless Mouse', category: 'Hardware', sku: 'HW-MS-01', stock: 15, unitPrice: 25 },
      { _id: 'prod-5', _creationTime: now, workspaceId: WORKSPACE_ID, name: 'Mobile App Development', category: 'Services', sku: 'SVC-MOB-01', stock: 9999, unitPrice: 150 },
      { _id: 'prod-6', _creationTime: now, workspaceId: WORKSPACE_ID, name: 'SEO Consultation', category: 'Services', sku: 'SVC-SEO-01', stock: 9999, unitPrice: 75 },
      { _id: 'prod-7', _creationTime: now, workspaceId: WORKSPACE_ID, name: 'USB Cable', category: 'Hardware', sku: 'HW-USB-01', stock: 50, unitPrice: 12 },
      { _id: 'prod-8', _creationTime: now, workspaceId: WORKSPACE_ID, name: 'Laptop Stand', category: 'Hardware', sku: 'HW-LS-01', stock: 5, unitPrice: 89 },
      { _id: 'prod-9', _creationTime: now, workspaceId: WORKSPACE_ID, name: 'Content Writing', category: 'Services', sku: 'SVC-CW-01', stock: 9999, unitPrice: 50 },
      { _id: 'prod-10', _creationTime: now, workspaceId: WORKSPACE_ID, name: 'Graphic Design', category: 'Services', sku: 'SVC-GD-01', stock: 9999, unitPrice: 85 },
  ] as Product[],
  taxRates: [
      { _id: 'tax-1', _creationTime: now, workspaceId: WORKSPACE_ID, name: 'No Tax', rate: 0, isDefault: true },
      { _id: 'tax-2', _creationTime: now, workspaceId: WORKSPACE_ID, name: 'Standard Rate', rate: 0.08, isDefault: false },
  ] as TaxRate[],
  invoices: [] as Invoice[],
  quotes: [] as Quote[],
  bills: [] as Bill[],
  purchaseOrders: [] as PurchaseOrder[],
  accounts: [] as Account[],
  journalEntries: [] as JournalEntry[],
  teamMembers: [
      { _id: 'tm-1', _creationTime: now, workspaceId: WORKSPACE_ID, userId: USER_ID, name: 'Jane Doe', email: '<EMAIL>', role: UserRole.Admin, status: 'Active' },
      { _id: 'tm-2', _creationTime: now, workspaceId: WORKSPACE_ID, userId: 'user-2', name: 'John Smith', email: '<EMAIL>', role: UserRole.Member, status: 'Invited' },
  ] as TeamMember[],
};

// Populate initial data that has dependencies
const accounts: Omit<Account, '_id' | '_creationTime' | 'workspaceId' >[] = [
    { code: '1010', name: 'Business Checking', type: AccountType.Asset, balance: 25000 },
    { code: '1200', name: 'Accounts Receivable', type: AccountType.Asset, balance: 7500 },
    { code: '2010', name: 'Accounts Payable', type: AccountType.Liability, balance: -2000 },
    { code: '2200', name: 'Tax Payable', type: AccountType.Liability, balance: -500 },
    { code: '3000', name: 'Owner\'s Equity', type: AccountType.Equity, balance: -30000 },
    { code: '4000', name: 'Service Revenue', type: AccountType.Revenue, balance: 0 },
    { code: '5000', name: 'Software Expense', type: AccountType.Expense, balance: 0 },
    { code: '5010', name: 'Miscellaneous Expense', type: AccountType.Expense, balance: 0 },
    { code: '5020', name: 'Rent Expense', type: AccountType.Expense, balance: 0 },
];

mockData.accounts = accounts.map(acc => ({ ...acc, _id: createId('acc'), _creationTime: now, workspaceId: WORKSPACE_ID })) as Account[];

const invoices: Omit<Invoice, '_id' | '_creationTime' | 'workspaceId' | 'amount'>[] = [
    { clientId: 'client-1', issueDate: '2024-05-15', dueDate: '2024-06-14', status: InvoiceStatus.Paid, items: [{ description: 'Web Dev Services', quantity: 50, unitPrice: 100, total: 5000, productId: 'prod-1' }], taxId: 'tax-1' },
    { clientId: 'client-2', issueDate: '2024-06-01', dueDate: '2024-07-01', status: InvoiceStatus.Sent, items: [{ description: 'UX Design Package', quantity: 1, unitPrice: 2500, total: 2500, productId: 'prod-2' }], taxId: 'tax-1' },
    { clientId: 'client-1', issueDate: '2024-04-10', dueDate: '2024-05-10', status: InvoiceStatus.Overdue, items: [{ description: 'UX Design Package', quantity: 2, unitPrice: 2500, total: 5000, productId: 'prod-2' }], taxId: 'tax-2', taxRate: 0.08 },
    { clientId: 'client-2', issueDate: '2024-06-10', dueDate: '2024-07-10', status: InvoiceStatus.Draft, items: [{ description: 'Web Dev Services', quantity: 10, unitPrice: 100, total: 1000, productId: 'prod-1' }], taxId: 'tax-1' },
];
mockData.invoices = invoices.map(inv => {
    const subtotal = inv.items.reduce((sum, item) => sum + item.total, 0);
    const taxRate = mockData.taxRates.find(t => t._id === inv.taxId)?.rate || 0;
    const amount = subtotal * (1 + taxRate);
    return { ...inv, _id: createId('inv'), _creationTime: now, workspaceId: WORKSPACE_ID, amount } as Invoice;
});


const bills: Omit<Bill, '_id' | '_creationTime' | 'workspaceId'>[] = [
    { vendor: 'Cloud Services Inc.', category: 'Software', description: 'Monthly subscription', date: '2024-06-01', dueDate: '2024-06-15', amount: 250, status: BillStatus.Paid, supplierId: 'sup-1' },
    { vendor: 'Office Supplies Co.', category: 'Supplies', description: 'Keyboards and mice', date: '2024-06-05', dueDate: '2024-07-05', amount: 800, status: BillStatus.Unpaid, supplierId: 'sup-2' },
    { vendor: 'Landlord Corp', category: 'Rent', description: 'June Rent', date: '2024-05-25', dueDate: '2024-06-01', amount: 1200, status: BillStatus.Overdue },
];
mockData.bills = bills.map(bill => ({ ...bill, _id: createId('bill'), _creationTime: now, workspaceId: WORKSPACE_ID }));

const quotes: Omit<Quote, '_id' | '_creationTime' | 'workspaceId' | 'amount'>[] = [
    { clientId: 'client-2', issueDate: '2024-06-02', expiryDate: '2024-07-02', status: QuoteStatus.Sent, items: [{ description: 'Web Dev Services', quantity: 20, unitPrice: 90, total: 1800, productId: 'prod-1' }], taxId: 'tax-1' },
    { clientId: 'client-1', issueDate: '2024-06-03', expiryDate: '2024-07-03', status: QuoteStatus.Accepted, items: [{ description: 'UX Design Package', quantity: 1, unitPrice: 2200, total: 2200, productId: 'prod-2' }], taxId: 'tax-1' },
];
mockData.quotes = quotes.map(q => {
    const subtotal = q.items.reduce((sum, item) => sum + item.total, 0);
    const taxRate = mockData.taxRates.find(t => t._id === q.taxId)?.rate || 0;
    const amount = subtotal * (1 + taxRate);
    return { ...q, _id: createId('qt'), _creationTime: now, workspaceId: WORKSPACE_ID, amount } as Quote;
});

const purchaseOrders: Omit<PurchaseOrder, '_id' | '_creationTime' | 'workspaceId' | 'amount'>[] = [
    { supplierId: 'sup-2', orderDate: '2024-06-10', expectedDelivery: '2024-06-24', status: PurchaseOrderStatus.Ordered, items: [{ description: 'Standard Keyboard', quantity: 20, unitPrice: 40, total: 800, productId: 'prod-3'}] },
];
mockData.purchaseOrders = purchaseOrders.map(po => {
     const amount = po.items.reduce((sum, item) => sum + item.total, 0);
    return { ...po, _id: createId('po'), _creationTime: now, workspaceId: WORKSPACE_ID, amount } as PurchaseOrder;
});

// Generate some journal entries for dashboard charts
const accountNameToIdMap = new Map(mockData.accounts.map(acc => [acc.name, acc._id]));

const journalEntriesTemplate = [
    {
        date: '2024-06-01', description: 'Paid monthly software subscription',
        debits: [{ accountName: 'Software Expense', amount: 250 }],
        credits: [{ accountName: 'Business Checking', amount: 250 }]
    },
    {
        date: '2024-05-20', description: 'Received payment for web services',
        debits: [{ accountName: 'Business Checking', amount: 5000 }],
        credits: [{ accountName: 'Accounts Receivable', amount: 5000 }]
    },
     {
        date: '2024-04-15', description: 'Client project revenue',
        debits: [{ accountName: 'Accounts Receivable', amount: 10000 }],
        credits: [{ accountName: 'Service Revenue', amount: 10000 }]
    },
    {
        date: '2024-04-01', description: 'Paid monthly rent',
        debits: [{ accountName: 'Rent Expense', amount: 1200 }],
        credits: [{ accountName: 'Business Checking', amount: 1200 }]
    }
];

mockData.journalEntries = journalEntriesTemplate.map(je => ({
    ...je,
    _id: createId('je'),
    _creationTime: now,
    workspaceId: WORKSPACE_ID,
    debits: je.debits.map(d => ({ ...d, accountName: d.accountName, accountId: accountNameToIdMap.get(d.accountName)! })),
    credits: je.credits.map(c => ({ ...c, accountName: c.accountName, accountId: accountNameToIdMap.get(c.accountName)! }))
})) as JournalEntry[];
