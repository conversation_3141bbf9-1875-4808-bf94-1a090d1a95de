{"name": "finhog-ai-accounting", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "convex:dev": "convex dev", "convex:deploy": "convex deploy"}, "dependencies": {"@convex-dev/auth": "^0.0.87", "@google/genai": "1.8.0", "@radix-ui/react-slot": "1.1.0", "class-variance-authority": "0.7.0", "clsx": "2.1.1", "convex": "^1.25.2", "framer-motion": "11.2.11", "lucide-react": "0.395.0", "react": "18.2.0", "react-dom": "18.2.0", "react-router-dom": "^6.30.1", "recharts": "2.12.7", "tailwind-merge": "2.3.0"}, "devDependencies": {"@tailwindcss/postcss7-compat": "^2.2.17", "@types/node": "^20.11.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^9.8.8", "postcss": "^7.0.39", "tailwindcss": "^3.4.1", "typescript": "~5.7.2", "vite": "^6.2.0"}}