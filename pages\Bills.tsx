
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Bill, BillStatus, ContextAwarePageProps, Account, JournalEntry } from '../types';
import Table from '../components/ui/Table';
import Button from '../components/ui/Button';
import Badge from '../components/ui/Badge';
import Card from '../components/ui/Card';
import ActionModal from '../components/ui/ActionModal';
import ConfirmationModal from '../components/ui/ConfirmationModal';
import BillStatusSummary from '../components/bills/BillStatusSummary';
import BillFilterBar from '../components/bills/BillFilterBar';
import BillDetailModal from '../components/bills/BillDetailModal';
import BillFormModal from '../components/bills/BillFormModal';
import AITransactionModal from '../components/transactions/AITransactionModal';
import { useQuery, useMutation } from "convex/react";
import { api } from "../convex/_generated/api";
import { isAIAvailable } from '../services/geminiService';

const createId = (prefix: string) => `${prefix}-${Math.random().toString(36).substr(2, 9)}`;

const Bills: React.FC<ContextAwarePageProps> = ({ workspaceId, context, clearPageContext }) => {
  // Fetch data from Convex
  const allBills = useQuery(api.bills.getBills, { workspaceId: workspaceId as any });
  const accounts = useQuery(api.accounts.getAccounts, { workspaceId: workspaceId as any });
  const suppliers = useQuery(api.suppliers.getSuppliers, { workspaceId: workspaceId as any });
  const products = useQuery(api.products.getProducts, { workspaceId: workspaceId as any });
  const taxRates = useQuery(api.taxRates.getTaxRates, { workspaceId: workspaceId as any });

  // Mutations
  const updateBillStatus = useMutation(api.bills.updateBillStatus);
  const deleteBill = useMutation(api.bills.deleteBill);
  const createBill = useMutation(api.bills.createBill);
  const updateBill = useMutation(api.bills.updateBill);

  const isLoading = allBills === undefined || accounts === undefined || suppliers === undefined || products === undefined || taxRates === undefined;

  const [selectedBill, setSelectedBill] = useState<Bill | null>(null);

  // Modal states
  const [isDetailOpen, setIsDetailOpen] = useState(false);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isConfirmDeleteOpen, setIsConfirmDeleteOpen] = useState(false);
  const [billToDelete, setBillToDelete] = useState<Bill | null>(null);
  const [isAITransactionModalOpen, setIsAITransactionModalOpen] = useState(false);
  const [initialAIPrompt, setInitialAIPrompt] = useState<string | undefined>(undefined);
  const [isActionModalOpen, setIsActionModalOpen] = useState(false);
  const [actionBill, setActionBill] = useState<Bill | null>(null);

  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'All' | BillStatus>('All');

  useEffect(() => {
    if (context?.filter) {
      setSearchTerm(context.filter);
      clearPageContext();
    }
  }, [context, clearPageContext]);

  const filteredBills = useMemo(() => {
    // Provide default empty array if allBills is undefined
    const safeBills = allBills || [];

    return safeBills
      .filter(bill => statusFilter === 'All' || bill.status === statusFilter)
      .filter(bill => {
        const term = searchTerm.toLowerCase();
        return !term || bill.vendor.toLowerCase().includes(term) || bill._id.toLowerCase().includes(term);
      });
  }, [allBills, searchTerm, statusFilter]);
  
  const formatCurrency = (amount: number) => new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);

  const handleSave = async (billData: Omit<Bill, '_id' | '_creationTime' | 'workspaceId'>, id?: string) => {
    if (!suppliers) return;

    try {
      if (id) {
        await updateBill({
          billId: id as any,
          supplierId: billData.supplierId as any,
          billNumber: billData.billNumber || `BILL-${Date.now().toString().slice(-6)}`,
          issueDate: billData.issueDate,
          dueDate: billData.dueDate,
          amount: billData.amount,
          status: billData.status,
          items: billData.items || [],
          taxId: billData.taxId,
          taxRate: billData.taxRate,
        });
      } else {
        await createBill({
          workspaceId: workspaceId as any,
          supplierId: billData.supplierId as any,
          billNumber: billData.billNumber || `BILL-${Date.now().toString().slice(-6)}`,
          issueDate: billData.issueDate,
          dueDate: billData.dueDate,
          amount: billData.amount,
          status: billData.status,
          items: billData.items || [],
          taxId: billData.taxId,
          taxRate: billData.taxRate,
        });
      }
      setIsFormOpen(false);
    } catch (error) {
      console.error('Failed to save bill:', error);
    }
  };

  const handleMarkAsPaid = async (bill: Bill) => {
    try {
      await updateBillStatus({
        billId: bill._id as any,
        status: BillStatus.Paid
      });
    } catch (error) {
      console.error('Failed to mark bill as paid:', error);
    }
  };
  
  const handleView = (bill: Bill) => {
    setSelectedBill(bill);
    setIsDetailOpen(true);
  };
  
  const handleNew = () => {
    setSelectedBill(null);
    setIsFormOpen(true);
  };

  const handleEdit = (bill: Bill) => {
    setSelectedBill(bill);
    setIsFormOpen(true);
  };
  
  const handleDeleteRequest = (bill: Bill) => {
    setBillToDelete(bill);
    setIsConfirmDeleteOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (billToDelete) {
      try {
        await deleteBill({ billId: billToDelete._id as any });
        setBillToDelete(null);
      } catch (error) {
        console.error('Failed to delete bill:', error);
      }
    }
    setIsConfirmDeleteOpen(false);
  };

  const handleOpenActionModal = (bill: Bill) => {
    setActionBill(bill);
    setIsActionModalOpen(true);
  };

  const getBillActions = (bill: Bill) => {
    const actions = [];

    // Status-specific actions
    if (bill.status === BillStatus.Unpaid || bill.status === BillStatus.Overdue) {
      actions.push({
        label: 'Mark as Paid',
        onClick: () => handleMarkAsPaid(bill),
        icon: (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        )
      });
    }

    // Common actions
    actions.push(
      {
        label: 'View',
        onClick: () => handleView(bill),
        icon: (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
          </svg>
        )
      },
      {
        label: 'Edit',
        onClick: () => handleEdit(bill),
        icon: (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
        )
      },
      {
        label: 'Delete',
        onClick: () => handleDeleteRequest(bill),
        variant: 'danger' as const,
        icon: (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        )
      }
    );

    return actions;
  };
  
  const handleGenericAITransactionRequest = () => {
    setInitialAIPrompt(undefined);
    setIsAITransactionModalOpen(true);
  };

  const handleAITransactionRequest = (bill: Bill) => {
    const prompt = `Record payment for bill ${bill.description} from ${bill.vendor} for ${formatCurrency(bill.amount)}.`;
    setInitialAIPrompt(prompt);
    setIsAITransactionModalOpen(true);
  };

  const handleJournalEntrySave = async (entryData: Omit<JournalEntry, '_id' | '_creationTime' | 'workspaceId'>) => {
    mockData.journalEntries.push({
        ...entryData,
        _id: createId('je'),
        _creationTime: Date.now(),
        workspaceId,
    });
    setIsAITransactionModalOpen(false);
    setInitialAIPrompt(undefined);
  };

  const columns = [
    { key: 'vendor' as keyof Bill, header: 'Vendor' },
    { key: 'category' as keyof Bill, header: 'Category' },
    { key: 'date' as keyof Bill, header: 'Date' },
    { key: 'dueDate' as keyof Bill, header: 'Due Date' },
    {
      key: 'amount' as keyof Bill,
      header: 'Amount',
      render: (item: Bill) => <span className="font-medium">{formatCurrency(item.amount)}</span>,
    },
    {
      key: 'status' as keyof Bill,
      header: 'Status',
      render: (item: Bill) => <Badge status={item.status} />,
    },
    {
      key: 'actions' as 'actions',
      header: '',
      render: (item: Bill) => (
        <div className="flex items-center justify-end space-x-2">
           <Button variant="secondary" size="sm" onClick={() => handleAITransactionRequest(item)} disabled={!isAIAvailable} title={!isAIAvailable ? "AI features unavailable" : "Record transaction using AI"}>
            Record AI
          </Button>
          <Button variant="ghost" size="sm" className="w-8 h-8 p-0" onClick={() => handleOpenActionModal(item)}>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" /></svg>
          </Button>
        </div>
      )
    },
  ];
  
  const aiDisabledTooltip = !isAIAvailable ? 'AI features are unavailable. API key not configured.' : '';

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-4">
        <h2 className="text-xl font-semibold text-gray-900">Bills & Expenses</h2>
        <div className="flex items-center gap-2">
          <Button variant="secondary" onClick={handleGenericAITransactionRequest} disabled={!isAIAvailable} title={aiDisabledTooltip}>Record with AI</Button>
          <Button variant="primary" onClick={handleNew}>New Bill</Button>
        </div>
      </div>
      
      <BillStatusSummary bills={allBills} isLoading={isLoading} />
      
      <BillFilterBar
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        statusFilter={statusFilter}
        setStatusFilter={setStatusFilter}
        onClear={() => setSearchTerm('')}
      />

      <Card>
        {isLoading ? (
          <div className="space-y-2 p-6">
            <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
            {[...Array(5)].map((_, i) => <div key={i} className="h-12 bg-gray-100 rounded animate-pulse"></div>)}
          </div>
        ) : (
          <Table<Bill> columns={columns} data={filteredBills.map(b => ({...b, id: b._id}))} />
        )}
      </Card>

      <BillDetailModal 
        bill={selectedBill}
        isOpen={isDetailOpen}
        onClose={() => setIsDetailOpen(false)}
      />
      
      <BillFormModal
        bill={selectedBill}
        isOpen={isFormOpen}
        onClose={() => setIsFormOpen(false)}
        onSave={(data) => handleSave(data, selectedBill?._id)}
        suppliers={suppliers || []}
        products={products || []}
        taxRates={taxRates || []}
      />

       <AITransactionModal
        isOpen={isAITransactionModalOpen}
        onClose={() => setIsAITransactionModalOpen(false)}
        onSave={handleJournalEntrySave}
        accounts={accounts || []}
        workspaceId={workspaceId}
        initialPrompt={initialAIPrompt}
      />

      <ConfirmationModal
        isOpen={isConfirmDeleteOpen}
        onClose={() => setIsConfirmDeleteOpen(false)}
        onConfirm={handleDeleteConfirm}
        title="Delete Bill"
        message={
            <span>
                Are you sure you want to delete the bill from <strong>{billToDelete?.vendor}</strong>?
                This action cannot be undone.
            </span>
        }
        confirmText="Delete Bill"
      />

      <ActionModal
        isOpen={isActionModalOpen}
        onClose={() => setIsActionModalOpen(false)}
        title={`Actions for Bill from ${actionBill?.vendor || ''}`}
        actions={actionBill ? getBillActions(actionBill) : []}
      />
    </div>
  );
};

export default Bills;
