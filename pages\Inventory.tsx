import React, { useState, useMemo } from 'react';
import { Product } from '../types';
import Table from '../components/ui/Table';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import Dropdown from '../components/ui/Dropdown';
import ConfirmationModal from '../components/ui/ConfirmationModal';
import InventoryStatusSummary from '../components/inventory/InventoryStatusSummary';
import InventoryFilterBar from '../components/inventory/InventoryFilterBar';
import ProductDetailModal from '../components/inventory/ProductDetailModal';
import ProductFormModal from '../components/inventory/ProductFormModal';
import { useQuery, useMutation } from "convex/react";
import { api } from "../convex/_generated/api";

interface InventoryProps {
  workspaceId: string;
}

const Inventory: React.FC<InventoryProps> = ({ workspaceId }) => {
  // Fetch data from Convex
  const allProducts = useQuery(api.products.getProducts, { workspaceId: workspaceId as any });

  // Mutations
  const createProduct = useMutation(api.products.createProduct);
  const updateProduct = useMutation(api.products.updateProduct);
  const deleteProduct = useMutation(api.products.deleteProduct);

  const isLoading = allProducts === undefined;

  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);

  // Modal states
  const [isDetailOpen, setIsDetailOpen] = useState(false);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isConfirmDeleteOpen, setIsConfirmDeleteOpen] = useState(false);
  const [productToDelete, setProductToDelete] = useState<Product | null>(null);

  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('All');

  const { filteredProducts, categories } = useMemo(() => {
    if (!allProducts) return { filteredProducts: [], categories: ['All'] };
    const uniqueCategories: string[] = ['All', ...new Set(allProducts.map(p => p.category))];
    const filtered = allProducts
      .filter(product => categoryFilter === 'All' || product.category === categoryFilter)
      .filter(product => {
        const term = searchTerm.toLowerCase();
        return !term || product.name.toLowerCase().includes(term) || (product.sku && product.sku.toLowerCase().includes(term));
      });
    return { filteredProducts: filtered, categories: uniqueCategories };
  }, [allProducts, searchTerm, categoryFilter]);

  const handleSave = async (productData: Omit<Product, '_id' | '_creationTime' | 'workspaceId'>, id?: string) => {
    try {
      if (id) {
        await updateProduct({
          productId: id as any,
          name: productData.name,
          category: productData.category,
          sku: productData.sku,
          stock: productData.stock,
          unitPrice: productData.unitPrice,
        });
      } else {
        await createProduct({
          workspaceId: workspaceId as any,
          name: productData.name,
          category: productData.category,
          sku: productData.sku,
          stock: productData.stock,
          unitPrice: productData.unitPrice,
        });
      }
      setIsFormOpen(false);
      setSelectedProduct(null);
    } catch (error) {
      console.error('Failed to save product:', error);
    }
  };

  const handleView = (product: Product) => {
    setSelectedProduct(product);
    setIsDetailOpen(true);
  };

  const handleNew = () => {
    setSelectedProduct(null);
    setIsFormOpen(true);
  };

  const handleEdit = (product: Product) => {
    setSelectedProduct(product);
    setIsFormOpen(true);
  };

  const handleDeleteRequest = (product: Product) => {
    setProductToDelete(product);
    setIsConfirmDeleteOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (productToDelete) {
      try {
        await deleteProduct({ productId: productToDelete._id as any });
        setProductToDelete(null);
      } catch (error) {
        console.error('Failed to delete product:', error);
      }
    }
    setIsConfirmDeleteOpen(false);
  };



  const formatCurrency = (amount: number) => new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);

  const columns = [
    { key: 'name' as keyof Product, header: 'Product Name' },
    { key: 'category' as keyof Product, header: 'Category' },
    { key: 'sku' as keyof Product, header: 'SKU' },
    { key: 'stock' as keyof Product, header: 'Stock' },
    {
      key: 'unitPrice' as keyof Product,
      header: 'Unit Price',
      render: (item: Product) => <span className="font-medium">{formatCurrency(item.unitPrice)}</span>,
    },
    {
      key: 'actions' as 'actions',
      header: '',
      render: (item: Product) => (
        <div className="text-right">
          <Dropdown
            trigger={
              <Button variant="ghost" size="sm" className="w-8 h-8 p-0">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" /></svg>
              </Button>
            }
          >
            <div className="py-1">
              <button onClick={() => handleView(item)} className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">View</button>
              <button onClick={() => handleEdit(item)} className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Edit</button>
              <div className="border-t border-gray-100 my-1"></div>
              <button onClick={() => handleDeleteRequest(item)} className="flex items-center w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-100">Delete</button>
            </div>
          </Dropdown>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-4">
        <h2 className="text-xl font-semibold text-gray-900">Inventory</h2>
        <Button variant="primary" onClick={handleNew}>New Product</Button>
      </div>

      <InventoryStatusSummary products={allProducts} isLoading={isLoading} />
      
      <InventoryFilterBar
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        categoryFilter={categoryFilter}
        setCategoryFilter={setCategoryFilter}
        categories={categories}
      />

      <Card>
        {isLoading ? (
          <div className="space-y-2 p-6">
            <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
            {[...Array(5)].map((_, i) => <div key={i} className="h-12 bg-gray-100 rounded animate-pulse"></div>)}
          </div>
        ) : (
          <Table<Product> columns={columns} data={filteredProducts.map(p => ({...p, id: p._id}))} />
        )}
      </Card>

      <ProductDetailModal 
        product={selectedProduct}
        isOpen={isDetailOpen}
        onClose={() => setIsDetailOpen(false)}
      />
      
      <ProductFormModal 
        product={selectedProduct}
        isOpen={isFormOpen}
        onClose={() => setIsFormOpen(false)}
        onSave={(data) => handleSave(data, selectedProduct?._id)}
      />

      <ConfirmationModal
        isOpen={isConfirmDeleteOpen}
        onClose={() => setIsConfirmDeleteOpen(false)}
        onConfirm={handleDeleteConfirm}
        title="Delete Product"
        message={
            <span>
                Are you sure you want to delete <strong>{productToDelete?.name}</strong>? 
                This action cannot be undone.
            </span>
        }
        confirmText="Delete Product"
      />
    </div>
  );
};

export default Inventory;