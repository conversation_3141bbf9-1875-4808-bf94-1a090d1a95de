
import React, { useState, useEffect, useMemo } from 'react';
import { PurchaseOrder, PurchaseOrderStatus, Supplier, Product, ContextAwarePageProps, Account, JournalEntry } from '../types';
import Table from '../components/ui/Table';
import Button from '../components/ui/Button';
import Badge from '../components/ui/Badge';
import Card from '../components/ui/Card';
import ActionModal from '../components/ui/ActionModal';
import ConfirmationModal from '../components/ui/ConfirmationModal';
import PurchaseStatusSummary from '../components/purchases/PurchaseStatusSummary';
import PurchaseFilterBar from '../components/purchases/PurchaseFilterBar';
import PurchaseDetailModal from '../components/purchases/PurchaseDetailModal';
import PurchaseFormModal from '../components/purchases/PurchaseFormModal';
import AITransactionModal from '../components/transactions/AITransactionModal';
import { mockData } from '../lib/mockData';
import { isAIAvailable } from '../services/geminiService';

const createId = (prefix: string) => `${prefix}-${Math.random().toString(36).substr(2, 9)}`;

const Purchases: React.FC<ContextAwarePageProps> = ({ workspaceId, context, clearPageContext }) => {
  const [allPurchaseOrders, setAllPurchaseOrders] = useState(() => mockData.purchaseOrders.filter(p => p.workspaceId === workspaceId));
  const suppliers = useMemo(() => mockData.suppliers.filter(s => s.workspaceId === workspaceId), [workspaceId]);
  const products = useMemo(() => mockData.products.filter(p => p.workspaceId === workspaceId), [workspaceId]);
  const accounts = useMemo(() => mockData.accounts.filter(a => a.workspaceId === workspaceId), [workspaceId]);
  const taxRates = useMemo(() => mockData.taxRates?.filter(t => t.workspaceId === workspaceId) || [], [workspaceId]);
  const isLoading = false;

  const [selectedPO, setSelectedPO] = useState<PurchaseOrder | null>(null);

  // Modal states
  const [isDetailOpen, setIsDetailOpen] = useState(false);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isConfirmDeleteOpen, setIsConfirmDeleteOpen] = useState(false);
  const [poToDelete, setPoToDelete] = useState<PurchaseOrder | null>(null);
  const [isAITransactionModalOpen, setIsAITransactionModalOpen] = useState(false);
  const [initialAIPrompt, setInitialAIPrompt] = useState<string | undefined>(undefined);
  const [isActionModalOpen, setIsActionModalOpen] = useState(false);
  const [actionPO, setActionPO] = useState<PurchaseOrder | null>(null);

  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'All' | PurchaseOrderStatus>('All');

  useEffect(() => {
    if (context?.filter) {
      setSearchTerm(context.filter);
      clearPageContext();
    }
  }, [context, clearPageContext]);

  const filteredPOs = useMemo(() => {
    if (!allPurchaseOrders || !suppliers) return [];
    return allPurchaseOrders
      .filter(po => statusFilter === 'All' || po.status === statusFilter)
      .filter(po => {
        const supplierName = suppliers.find(s => s._id === po.supplierId)?.name || '';
        const term = searchTerm.toLowerCase();
        return !term || supplierName.toLowerCase().includes(term) || po._id.toLowerCase().includes(term);
      });
  }, [allPurchaseOrders, suppliers, searchTerm, statusFilter]);
  
  const formatCurrency = (amount: number) => new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);
  
  const handleSave = async (poData: Omit<PurchaseOrder, '_id' | '_creationTime' | 'workspaceId' | 'amount'>, id?: string) => {
    const totalAmount = poData.items.reduce((sum, item) => sum + item.total, 0);
    const dataToSave = { ...poData, amount: totalAmount };
    
    if (id) {
        const index = mockData.purchaseOrders.findIndex(p => p._id === id);
        if(index > -1) {
            mockData.purchaseOrders[index] = { ...mockData.purchaseOrders[index], ...dataToSave };
        }
    } else {
        const newPO: PurchaseOrder = {
            ...dataToSave,
            _id: createId('po'),
            _creationTime: Date.now(),
            workspaceId
        };
        mockData.purchaseOrders.push(newPO);
    }

    setAllPurchaseOrders([...mockData.purchaseOrders.filter(p => p.workspaceId === workspaceId)]);
    setIsFormOpen(false);
    setSelectedPO(null);
  };

  const handleAction = async (po: PurchaseOrder, newStatus: PurchaseOrderStatus) => {
    await handleSave({ ...po, status: newStatus }, po._id);
  };
  
  const handleView = (po: PurchaseOrder) => {
    setSelectedPO(po);
    setIsDetailOpen(true);
  };
  
  const handleNew = () => {
    setSelectedPO(null);
    setIsFormOpen(true);
  };

  const handleEdit = (po: PurchaseOrder) => {
    setSelectedPO(po);
    setIsFormOpen(true);
  };
  
  const handleDeleteRequest = (po: PurchaseOrder) => {
    setPoToDelete(po);
    setIsConfirmDeleteOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (poToDelete) {
      mockData.purchaseOrders = mockData.purchaseOrders.filter(p => p._id !== poToDelete._id);
      setAllPurchaseOrders(mockData.purchaseOrders.filter(p => p.workspaceId === workspaceId));
      setPoToDelete(null);
    }
    setIsConfirmDeleteOpen(false);
  };

  const handleGenericAITransactionRequest = () => {
    setInitialAIPrompt(undefined);
    setIsAITransactionModalOpen(true);
  };

  const handleAITransactionRequest = (po: PurchaseOrder) => {
    const supplierName = suppliers?.find(s => s._id === po.supplierId)?.name || 'the supplier';
    const prompt = `Record payment for purchase order ${po._id.slice(-6).toUpperCase()} to ${supplierName} for ${formatCurrency(po.amount)}.`;
    setInitialAIPrompt(prompt);
    setIsAITransactionModalOpen(true);
  };

  const handleOpenActionModal = (po: PurchaseOrder) => {
    setActionPO(po);
    setIsActionModalOpen(true);
  };

  const getPurchaseOrderActions = (po: PurchaseOrder) => {
    const actions = [];

    // Status-specific actions
    if (po.status === PurchaseOrderStatus.Draft) {
      actions.push({
        label: 'Place Order',
        onClick: () => handleAction(po, PurchaseOrderStatus.Ordered),
        icon: (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
          </svg>
        )
      });
    }

    if (po.status === PurchaseOrderStatus.Ordered) {
      actions.push({
        label: 'Mark as Completed',
        onClick: () => handleAction(po, PurchaseOrderStatus.Completed),
        icon: (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        )
      });
    }

    // Common actions
    actions.push(
      {
        label: 'View',
        onClick: () => handleView(po),
        icon: (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
          </svg>
        )
      },
      {
        label: 'Edit',
        onClick: () => handleEdit(po),
        disabled: po.status !== 'Draft',
        icon: (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
        )
      },
      {
        label: 'Delete',
        onClick: () => handleDeleteRequest(po),
        variant: 'danger' as const,
        icon: (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        )
      }
    );

    return actions;
  };

  const handleJournalEntrySave = async (entryData: Omit<JournalEntry, '_id' | '_creationTime' | 'workspaceId'>) => {
    mockData.journalEntries.push({
        ...entryData,
        _id: createId('je'),
        _creationTime: Date.now(),
        workspaceId,
    });
    setIsAITransactionModalOpen(false);
    setInitialAIPrompt(undefined);
  };

  const columns = [
    { key: '_id' as keyof PurchaseOrder, header: 'PO Number', render: (item: PurchaseOrder) => item._id.slice(-6).toUpperCase() },
    {
      key: 'supplierId' as keyof PurchaseOrder,
      header: 'Supplier',
      render: (item: PurchaseOrder) => suppliers?.find(s => s._id === item.supplierId)?.name || 'Unknown',
    },
    { key: 'orderDate' as keyof PurchaseOrder, header: 'Order Date' },
    { key: 'expectedDelivery' as keyof PurchaseOrder, header: 'Expected Delivery' },
    {
      key: 'amount' as keyof PurchaseOrder,
      header: 'Amount',
      render: (item: PurchaseOrder) => <span className="font-medium">{formatCurrency(item.amount)}</span>,
    },
    {
      key: 'status' as keyof PurchaseOrder,
      header: 'Status',
      render: (item: PurchaseOrder) => <Badge status={item.status} />,
    },
    {
      key: 'actions' as 'actions',
      header: '',
      render: (item: PurchaseOrder) => (
        <div className="flex items-center justify-end space-x-2">
          <Button variant="secondary" size="sm" onClick={() => handleAITransactionRequest(item)} disabled={!isAIAvailable} title={!isAIAvailable ? "AI features unavailable" : "Record transaction using AI"}>
              Record AI
          </Button>
          <Button variant="ghost" size="sm" className="w-8 h-8 p-0" onClick={() => handleOpenActionModal(item)}>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" /></svg>
          </Button>
        </div>
      )
    }
  ];

  const aiDisabledTooltip = !isAIAvailable ? 'AI features are unavailable. API key not configured.' : '';

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-4">
        <h2 className="text-xl font-semibold text-gray-900">Purchase Orders</h2>
        <div className="flex items-center gap-2">
          <Button variant="secondary" onClick={handleGenericAITransactionRequest} disabled={!isAIAvailable} title={aiDisabledTooltip}>Record with AI</Button>
          <Button variant="primary" onClick={handleNew}>New Purchase Order</Button>
        </div>
      </div>
      
      <PurchaseStatusSummary purchaseOrders={allPurchaseOrders} isLoading={isLoading} />
      
      <PurchaseFilterBar
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        statusFilter={statusFilter}
        setStatusFilter={setStatusFilter}
        onClear={() => setSearchTerm('')}
      />

      <Card>
        {isLoading ? (
          <div className="space-y-2 p-6">
            <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
            {[...Array(4)].map((_, i) => <div key={i} className="h-12 bg-gray-100 rounded animate-pulse"></div>)}
          </div>
        ) : (
          <Table<PurchaseOrder> columns={columns} data={filteredPOs.map(po => ({...po, id: po._id}))} />
        )}
      </Card>

      <PurchaseDetailModal 
        purchaseOrder={selectedPO}
        isOpen={isDetailOpen}
        onClose={() => setIsDetailOpen(false)}
        supplierName={suppliers?.find(s => s._id === selectedPO?.supplierId)?.name}
      />
      
      <PurchaseFormModal
        purchaseOrder={selectedPO}
        isOpen={isFormOpen}
        onClose={() => setIsFormOpen(false)}
        onSave={(data) => handleSave(data, selectedPO?._id)}
        suppliers={suppliers || []}
        products={products || []}
        taxRates={taxRates || []}
      />

      <AITransactionModal
        isOpen={isAITransactionModalOpen}
        onClose={() => setIsAITransactionModalOpen(false)}
        onSave={handleJournalEntrySave}
        accounts={accounts || []}
        workspaceId={workspaceId}
        initialPrompt={initialAIPrompt}
      />

      <ConfirmationModal
        isOpen={isConfirmDeleteOpen}
        onClose={() => setIsConfirmDeleteOpen(false)}
        onConfirm={handleDeleteConfirm}
        title="Delete Purchase Order"
        message={
            <span>
                Are you sure you want to delete PO <strong>{poToDelete?._id.slice(-6).toUpperCase()}</strong>?
                This action cannot be undone.
            </span>
        }
        confirmText="Delete PO"
      />

      <ActionModal
        isOpen={isActionModalOpen}
        onClose={() => setIsActionModalOpen(false)}
        title={`Actions for PO ${actionPO?._id.slice(-6).toUpperCase() || ''}`}
        actions={actionPO ? getPurchaseOrderActions(actionPO) : []}
      />
    </div>
  );
};

export default Purchases;
