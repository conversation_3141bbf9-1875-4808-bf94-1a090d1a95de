
import React, { useState, useEffect, useMemo } from 'react';
import { PurchaseOrder, PurchaseOrderStatus, Supplier, Product, ContextAwarePageProps, Account, JournalEntry } from '../types';
import Table from '../components/ui/Table';
import Button from '../components/ui/Button';
import Badge from '../components/ui/Badge';
import Card from '../components/ui/Card';
import Dropdown from '../components/ui/Dropdown';
import ConfirmationModal from '../components/ui/ConfirmationModal';
import PurchaseStatusSummary from '../components/purchases/PurchaseStatusSummary';
import PurchaseFilterBar from '../components/purchases/PurchaseFilterBar';
import PurchaseDetailModal from '../components/purchases/PurchaseDetailModal';
import PurchaseFormModal from '../components/purchases/PurchaseFormModal';
import AITransactionModal from '../components/transactions/AITransactionModal';
import { mockData } from '../lib/mockData';
import { isAIAvailable } from '../services/geminiService';

const createId = (prefix: string) => `${prefix}-${Math.random().toString(36).substr(2, 9)}`;

const Purchases: React.FC<ContextAwarePageProps> = ({ workspaceId, context, clearPageContext }) => {
  const [allPurchaseOrders, setAllPurchaseOrders] = useState(() => mockData.purchaseOrders.filter(p => p.workspaceId === workspaceId));
  const suppliers = useMemo(() => mockData.suppliers.filter(s => s.workspaceId === workspaceId), [workspaceId]);
  const products = useMemo(() => mockData.products.filter(p => p.workspaceId === workspaceId), [workspaceId]);
  const accounts = useMemo(() => mockData.accounts.filter(a => a.workspaceId === workspaceId), [workspaceId]);
  const isLoading = false;

  const [selectedPO, setSelectedPO] = useState<PurchaseOrder | null>(null);

  // Modal states
  const [isDetailOpen, setIsDetailOpen] = useState(false);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isConfirmDeleteOpen, setIsConfirmDeleteOpen] = useState(false);
  const [poToDelete, setPoToDelete] = useState<PurchaseOrder | null>(null);
  const [isAITransactionModalOpen, setIsAITransactionModalOpen] = useState(false);
  const [initialAIPrompt, setInitialAIPrompt] = useState<string | undefined>(undefined);

  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'All' | PurchaseOrderStatus>('All');

  useEffect(() => {
    if (context?.filter) {
      setSearchTerm(context.filter);
      clearPageContext();
    }
  }, [context, clearPageContext]);

  const filteredPOs = useMemo(() => {
    if (!allPurchaseOrders || !suppliers) return [];
    return allPurchaseOrders
      .filter(po => statusFilter === 'All' || po.status === statusFilter)
      .filter(po => {
        const supplierName = suppliers.find(s => s._id === po.supplierId)?.name || '';
        const term = searchTerm.toLowerCase();
        return !term || supplierName.toLowerCase().includes(term) || po._id.toLowerCase().includes(term);
      });
  }, [allPurchaseOrders, suppliers, searchTerm, statusFilter]);
  
  const formatCurrency = (amount: number) => new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);
  
  const handleSave = async (poData: Omit<PurchaseOrder, '_id' | '_creationTime' | 'workspaceId' | 'amount'>, id?: string) => {
    const totalAmount = poData.items.reduce((sum, item) => sum + item.total, 0);
    const dataToSave = { ...poData, amount: totalAmount };
    
    if (id) {
        const index = mockData.purchaseOrders.findIndex(p => p._id === id);
        if(index > -1) {
            mockData.purchaseOrders[index] = { ...mockData.purchaseOrders[index], ...dataToSave };
        }
    } else {
        const newPO: PurchaseOrder = {
            ...dataToSave,
            _id: createId('po'),
            _creationTime: Date.now(),
            workspaceId
        };
        mockData.purchaseOrders.push(newPO);
    }

    setAllPurchaseOrders([...mockData.purchaseOrders.filter(p => p.workspaceId === workspaceId)]);
    setIsFormOpen(false);
    setSelectedPO(null);
  };

  const handleAction = async (po: PurchaseOrder, newStatus: PurchaseOrderStatus) => {
    await handleSave({ ...po, status: newStatus }, po._id);
  };
  
  const handleView = (po: PurchaseOrder) => {
    setSelectedPO(po);
    setIsDetailOpen(true);
  };
  
  const handleNew = () => {
    setSelectedPO(null);
    setIsFormOpen(true);
  };

  const handleEdit = (po: PurchaseOrder) => {
    setSelectedPO(po);
    setIsFormOpen(true);
  };
  
  const handleDeleteRequest = (po: PurchaseOrder) => {
    setPoToDelete(po);
    setIsConfirmDeleteOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (poToDelete) {
      mockData.purchaseOrders = mockData.purchaseOrders.filter(p => p._id !== poToDelete._id);
      setAllPurchaseOrders(mockData.purchaseOrders.filter(p => p.workspaceId === workspaceId));
      setPoToDelete(null);
    }
    setIsConfirmDeleteOpen(false);
  };

  const handleGenericAITransactionRequest = () => {
    setInitialAIPrompt(undefined);
    setIsAITransactionModalOpen(true);
  };

  const handleAITransactionRequest = (po: PurchaseOrder) => {
    const supplierName = suppliers?.find(s => s._id === po.supplierId)?.name || 'the supplier';
    const prompt = `Record payment for purchase order ${po._id.slice(-6).toUpperCase()} to ${supplierName} for ${formatCurrency(po.amount)}.`;
    setInitialAIPrompt(prompt);
    setIsAITransactionModalOpen(true);
  };

  const handleJournalEntrySave = async (entryData: Omit<JournalEntry, '_id' | '_creationTime' | 'workspaceId'>) => {
    mockData.journalEntries.push({
        ...entryData,
        _id: createId('je'),
        _creationTime: Date.now(),
        workspaceId,
    });
    setIsAITransactionModalOpen(false);
    setInitialAIPrompt(undefined);
  };

  const columns = [
    { key: '_id' as keyof PurchaseOrder, header: 'PO Number', render: (item: PurchaseOrder) => item._id.slice(-6).toUpperCase() },
    {
      key: 'supplierId' as keyof PurchaseOrder,
      header: 'Supplier',
      render: (item: PurchaseOrder) => suppliers?.find(s => s._id === item.supplierId)?.name || 'Unknown',
    },
    { key: 'orderDate' as keyof PurchaseOrder, header: 'Order Date' },
    { key: 'expectedDelivery' as keyof PurchaseOrder, header: 'Expected Delivery' },
    {
      key: 'amount' as keyof PurchaseOrder,
      header: 'Amount',
      render: (item: PurchaseOrder) => <span className="font-medium">{formatCurrency(item.amount)}</span>,
    },
    {
      key: 'status' as keyof PurchaseOrder,
      header: 'Status',
      render: (item: PurchaseOrder) => <Badge status={item.status} />,
    },
    {
      key: 'actions' as 'actions',
      header: '',
      render: (item: PurchaseOrder) => (
        <div className="flex items-center justify-end space-x-2">
          <Button variant="secondary" size="sm" onClick={() => handleAITransactionRequest(item)} disabled={!isAIAvailable} title={!isAIAvailable ? "AI features unavailable" : "Record transaction using AI"}>
              Record AI
          </Button>
          <Dropdown
            trigger={
              <Button variant="ghost" size="sm" className="w-8 h-8 p-0">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" /></svg>
              </Button>
            }
          >
            <div className="py-1">
              {item.status === PurchaseOrderStatus.Draft && (
                <button onClick={() => handleAction(item, PurchaseOrderStatus.Ordered)} className="flex items-center w-full text-left px-4 py-2 text-sm text-black hover:bg-black hover:text-white">Place Order</button>
              )}
               {item.status === PurchaseOrderStatus.Ordered && (
                <button onClick={() => handleAction(item, PurchaseOrderStatus.Completed)} className="flex items-center w-full text-left px-4 py-2 text-sm text-black hover:bg-black hover:text-white">Mark as Completed</button>
              )}
              <div className="border-t border-gray-100 my-1"></div>
              <button onClick={() => handleView(item)} className="flex items-center w-full text-left px-4 py-2 text-sm text-black hover:bg-black hover:text-white">View</button>
              <button onClick={() => handleEdit(item)} className="flex items-center w-full text-left px-4 py-2 text-sm text-black hover:bg-black hover:text-white" disabled={item.status !== 'Draft'}>Edit</button>
              <div className="border-t border-gray-100 my-1"></div>
              <button onClick={() => handleDeleteRequest(item)} className="flex items-center w-full text-left px-4 py-2 text-sm text-black hover:bg-black hover:text-white">Delete</button>
            </div>
          </Dropdown>
        </div>
      )
    }
  ];

  const aiDisabledTooltip = !isAIAvailable ? 'AI features are unavailable. API key not configured.' : '';

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-4">
        <h2 className="text-xl font-semibold text-gray-900">Purchase Orders</h2>
        <div className="flex items-center gap-2">
          <Button variant="secondary" onClick={handleGenericAITransactionRequest} disabled={!isAIAvailable} title={aiDisabledTooltip}>Record with AI</Button>
          <Button variant="primary" onClick={handleNew}>New Purchase Order</Button>
        </div>
      </div>
      
      <PurchaseStatusSummary purchaseOrders={allPurchaseOrders} isLoading={isLoading} />
      
      <PurchaseFilterBar
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        statusFilter={statusFilter}
        setStatusFilter={setStatusFilter}
        onClear={() => setSearchTerm('')}
      />

      <Card>
        {isLoading ? (
          <div className="space-y-2 p-6">
            <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
            {[...Array(4)].map((_, i) => <div key={i} className="h-12 bg-gray-100 rounded animate-pulse"></div>)}
          </div>
        ) : (
          <Table<PurchaseOrder> columns={columns} data={filteredPOs.map(po => ({...po, id: po._id}))} />
        )}
      </Card>

      <PurchaseDetailModal 
        purchaseOrder={selectedPO}
        isOpen={isDetailOpen}
        onClose={() => setIsDetailOpen(false)}
        supplierName={suppliers?.find(s => s._id === selectedPO?.supplierId)?.name}
      />
      
      <PurchaseFormModal 
        purchaseOrder={selectedPO}
        isOpen={isFormOpen}
        onClose={() => setIsFormOpen(false)}
        onSave={(data) => handleSave(data, selectedPO?._id)}
        suppliers={suppliers || []}
        products={products || []}
      />

      <AITransactionModal
        isOpen={isAITransactionModalOpen}
        onClose={() => setIsAITransactionModalOpen(false)}
        onSave={handleJournalEntrySave}
        accounts={accounts || []}
        workspaceId={workspaceId}
        initialPrompt={initialAIPrompt}
      />

      <ConfirmationModal
        isOpen={isConfirmDeleteOpen}
        onClose={() => setIsConfirmDeleteOpen(false)}
        onConfirm={handleDeleteConfirm}
        title="Delete Purchase Order"
        message={
            <span>
                Are you sure you want to delete PO <strong>{poToDelete?._id.slice(-6).toUpperCase()}</strong>? 
                This action cannot be undone.
            </span>
        }
        confirmText="Delete PO"
      />
    </div>
  );
};

export default Purchases;
