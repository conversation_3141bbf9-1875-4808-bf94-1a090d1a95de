
import React, { useState, useMemo } from 'react';
import { Quote, QuoteStatus, Invoice, InvoiceStatus, Client, Product, CompanyProfile, TaxRate, Account, JournalEntry } from '../types';
import Table from '../components/ui/Table';
import Button from '../components/ui/Button';
import Badge from '../components/ui/Badge';
import Card from '../components/ui/Card';
import Dropdown from '../components/ui/Dropdown';
import ConfirmationModal from '../components/ui/ConfirmationModal';
import QuoteStatusSummary from '../components/quotes/QuoteStatusSummary';
import QuoteFilterBar from '../components/quotes/QuoteFilterBar';
import QuoteDetailModal from '../components/quotes/QuoteDetailModal';
import QuoteFormModal from '../components/quotes/QuoteFormModal';
import AITransactionModal from '../components/transactions/AITransactionModal';
import { useQuery, useMutation } from "convex/react";
import { api } from "../convex/_generated/api";
import { isAIAvailable } from '../services/geminiService';

const createId = (prefix: string) => `${prefix}-${Math.random().toString(36).substr(2, 9)}`;

interface QuotesProps {
    workspaceId: string;
}

const Quotes: React.FC<QuotesProps> = ({ workspaceId }) => {
  // Fetch data from Convex
  const allQuotes = useQuery(api.quotes.getQuotes, { workspaceId: workspaceId as any });
  const clients = useQuery(api.clients.getClients, { workspaceId: workspaceId as any });
  const products = useQuery(api.products.getProducts, { workspaceId: workspaceId as any });
  const accounts = useQuery(api.accounts.getAccounts, { workspaceId: workspaceId as any });
  const companyProfile = useQuery(api.companyProfiles.getCompanyProfile, { workspaceId: workspaceId as any });
  const taxRates = useQuery(api.taxRates.getTaxRates, { workspaceId: workspaceId as any });

  // Mutations
  const updateQuoteStatus = useMutation(api.quotes.updateQuoteStatus);
  const deleteQuote = useMutation(api.quotes.deleteQuote);
  const createQuote = useMutation(api.quotes.createQuote);
  const updateQuote = useMutation(api.quotes.updateQuote);
  const convertToInvoice = useMutation(api.quotes.convertToInvoice);

  const isLoading = allQuotes === undefined || clients === undefined || products === undefined || accounts === undefined || companyProfile === undefined || taxRates === undefined;

  const [selectedQuote, setSelectedQuote] = useState<Quote | null>(null);
  
  // Modal states
  const [isDetailOpen, setIsDetailOpen] = useState(false);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isConfirmDeleteOpen, setIsConfirmDeleteOpen] = useState(false);
  const [quoteToDelete, setQuoteToDelete] = useState<Quote | null>(null);
  const [isAITransactionModalOpen, setIsAITransactionModalOpen] = useState(false);
  const [initialAIPrompt, setInitialAIPrompt] = useState<string | undefined>(undefined);
  
  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'All' | QuoteStatus>('All');

  const filteredQuotes = useMemo(() => {
    if (!allQuotes || !clients) return [];
    return allQuotes
      .filter(quote => statusFilter === 'All' || quote.status === statusFilter)
      .filter(quote => {
        const clientName = clients.find(c => c._id === quote.clientId)?.name || '';
        const term = searchTerm.toLowerCase();
        return !term || clientName.toLowerCase().includes(term) || quote._id.toLowerCase().includes(term);
      });
  }, [allQuotes, clients, searchTerm, statusFilter]);

  const formatCurrency = (amount: number) => new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);
  
  const handleSave = async (quoteData: Omit<Quote, '_id' | '_creationTime' | 'workspaceId' | 'amount' | 'taxRate'>, id?: string) => {
    if (!clients || !products) return;

    const subtotal = quoteData.items.reduce((sum, item) => sum + item.total, 0);
    const taxRateValue = 0.1; // Default 10% tax rate
    const totalAmount = subtotal * (1 + taxRateValue);

    const dataToSave = {
      ...quoteData,
      taxRate: taxRateValue,
      amount: totalAmount,
      workspaceId: workspaceId as any
    };

    try {
      if (id) {
        await updateQuote({
          quoteId: id as any,
          ...dataToSave
        });
      } else {
        await createQuote({
          ...dataToSave
        });
      }
      setIsFormOpen(false);
      setSelectedQuote(null);
    } catch (error) {
      console.error('Failed to save quote:', error);
    }
  };
  
  const handleAction = async (quote: Quote, newStatus: QuoteStatus) => {
    try {
      await updateQuoteStatus({
        quoteId: quote._id as any,
        status: newStatus
      });
    } catch (error) {
      console.error('Failed to update quote status:', error);
    }
  };
  
  const handleConvertToInvoice = async (quote: Quote) => {
    try {
      const dueDate = new Date(new Date().setDate(new Date().getDate() + 30)).toISOString().split('T')[0];
      const invoiceNumber = `INV-${Date.now().toString().slice(-6)}`;

      await convertToInvoice({
        quoteId: quote._id as any,
        invoiceNumber,
        dueDate
      });
    } catch (error) {
      console.error('Failed to convert quote to invoice:', error);
    }
  };

  const handleView = (quote: Quote) => {
    setSelectedQuote(quote);
    setIsDetailOpen(true);
  };
  
  const handleNew = () => {
    setSelectedQuote(null);
    setIsFormOpen(true);
  };

  const handleEdit = (quote: Quote) => {
    setSelectedQuote(quote);
    setIsFormOpen(true);
  };
  
  const handleDeleteRequest = (quote: Quote) => {
    setQuoteToDelete(quote);
    setIsConfirmDeleteOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (quoteToDelete) {
      mockData.quotes = mockData.quotes.filter(q => q._id !== quoteToDelete._id);
      setAllQuotes(mockData.quotes.filter(q => q.workspaceId === workspaceId));
      setQuoteToDelete(null);
    }
    setIsConfirmDeleteOpen(false);
  };

  const handleGenericAITransactionRequest = () => {
    setInitialAIPrompt(undefined);
    setIsAITransactionModalOpen(true);
  };

  const handleAITransactionRequest = (quote: Quote) => {
    const clientName = clients?.find(c => c._id === quote.clientId)?.name || 'the client';
    const prompt = `Record a deposit or initial payment for accepted quote ${quote._id.slice(-6).toUpperCase()} from ${clientName}.`;
    setInitialAIPrompt(prompt);
    setIsAITransactionModalOpen(true);
  };

  const handleJournalEntrySave = async (entryData: Omit<JournalEntry, '_id' | '_creationTime' | 'workspaceId'>) => {
    mockData.journalEntries.push({
        ...entryData,
        _id: createId('je'),
        _creationTime: Date.now(),
        workspaceId,
    });
    setIsAITransactionModalOpen(false);
    setInitialAIPrompt(undefined);
  };

  const columns = [
    { key: '_id' as keyof Quote, header: 'Quote ID', render: (item: Quote) => item._id.slice(-6).toUpperCase() },
    {
      key: 'clientId' as keyof Quote,
      header: 'Customer',
      render: (item: Quote) => clients?.find(c => c._id === item.clientId)?.name || 'Unknown Client',
    },
    { key: 'issueDate' as keyof Quote, header: 'Issued' },
    { key: 'expiryDate' as keyof Quote, header: 'Expires' },
    {
      key: 'amount' as keyof Quote,
      header: 'Amount',
      render: (item: Quote) => <span className="font-medium">{formatCurrency(item.amount)}</span>,
    },
    {
      key: 'status' as keyof Quote,
      header: 'Status',
      render: (item: Quote) => <Badge status={item.status} />,
    },
    {
      key: 'actions' as 'actions',
      header: '',
      render: (item: Quote) => (
        <div className="flex items-center justify-end space-x-2">
          {item.status === QuoteStatus.Accepted && (
            <Button variant="secondary" size="sm" onClick={() => handleAITransactionRequest(item)} disabled={!isAIAvailable} title={!isAIAvailable ? "AI features unavailable" : "Record transaction using AI"}>
              Record AI
            </Button>
          )}
          <Dropdown
            trigger={
              <Button variant="ghost" size="sm" className="w-8 h-8 p-0">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" /></svg>
              </Button>
            }
          >
            <div className="py-1">
              {item.status === QuoteStatus.Draft && (
                <button onClick={() => handleAction(item, QuoteStatus.Sent)} className="flex items-center w-full text-left px-4 py-2 text-sm text-black hover:bg-black hover:text-white">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor"><path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.428A1 1 0 009.894 16L4.894 4.571l7-14z" /></svg>
                  Send Quote
                </button>
              )}
               {item.status === QuoteStatus.Sent && (
                <>
                  <button onClick={() => handleAction(item, QuoteStatus.Accepted)} className="flex items-center w-full text-left px-4 py-2 text-sm text-black hover:bg-black hover:text-white">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" /></svg>
                    Accept
                  </button>
                  <button onClick={() => handleAction(item, QuoteStatus.Declined)} className="flex items-center w-full text-left px-4 py-2 text-sm text-black hover:bg-black hover:text-white">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" /></svg>
                    Decline
                  </button>
                </>
              )}
              {item.status === QuoteStatus.Accepted && (
                <button onClick={() => handleConvertToInvoice(item)} className="flex items-center w-full text-left px-4 py-2 text-sm text-black hover:bg-black hover:text-white">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor"><path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" /><path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2H6zM8 7a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1zm-1 4a1 1 0 100 2h4a1 1 0 100-2H7z" /></svg>
                  Convert to Invoice
                </button>
              )}
              <div className="border-t border-gray-100 my-1"></div>
              <button onClick={() => handleView(item)} className="flex items-center w-full text-left px-4 py-2 text-sm text-black hover:bg-black hover:text-white">
                View
              </button>
              <button onClick={() => handleEdit(item)} className="flex items-center w-full text-left px-4 py-2 text-sm text-black hover:bg-black hover:text-white">
                Edit
              </button>
              <div className="border-t border-gray-100 my-1"></div>
              <button onClick={() => handleDeleteRequest(item)} className="flex items-center w-full text-left px-4 py-2 text-sm text-black hover:bg-black hover:text-white">
                Delete
              </button>
            </div>
          </Dropdown>
        </div>
      )
    }
  ];

  const aiDisabledTooltip = !isAIAvailable ? 'AI features are unavailable. API key not configured.' : '';

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-4">
        <h2 className="text-xl font-semibold text-gray-900">Quotes</h2>
        <div className="flex items-center gap-2">
          <Button variant="secondary" onClick={handleGenericAITransactionRequest} disabled={!isAIAvailable} title={aiDisabledTooltip}>Record with AI</Button>
          <Button variant="primary" onClick={handleNew}>New Quote</Button>
        </div>
      </div>
      
      <QuoteStatusSummary quotes={allQuotes} isLoading={isLoading} />
      
      <QuoteFilterBar
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        statusFilter={statusFilter}
        setStatusFilter={setStatusFilter}
      />

      <Card>
        {isLoading ? (
          <div className="space-y-2 p-6">
            <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
            {[...Array(4)].map((_, i) => <div key={i} className="h-12 bg-gray-100 rounded animate-pulse"></div>)}
          </div>
        ) : (
          <Table<Quote> columns={columns} data={filteredQuotes.map(q => ({...q, id: q._id}))} />
        )}
      </Card>

      <QuoteDetailModal 
        quote={selectedQuote}
        isOpen={isDetailOpen}
        onClose={() => setIsDetailOpen(false)}
        client={clients?.find(c => c._id === selectedQuote?.clientId)}
        companyProfile={companyProfile || null}
      />
      
      <QuoteFormModal 
        quote={selectedQuote}
        isOpen={isFormOpen}
        onClose={() => setIsFormOpen(false)}
        onSave={(data) => handleSave(data, selectedQuote?._id)}
        clients={clients || []}
        products={products || []}
        taxRates={taxRates || []}
      />
      
      <AITransactionModal
        isOpen={isAITransactionModalOpen}
        onClose={() => setIsAITransactionModalOpen(false)}
        onSave={handleJournalEntrySave}
        accounts={accounts || []}
        workspaceId={workspaceId}
        initialPrompt={initialAIPrompt}
      />

      <ConfirmationModal
        isOpen={isConfirmDeleteOpen}
        onClose={() => setIsConfirmDeleteOpen(false)}
        onConfirm={handleDeleteConfirm}
        title="Delete Quote"
        message={
            <span>
                Are you sure you want to delete quote <strong>{quoteToDelete?._id.slice(-6).toUpperCase()}</strong>? 
                This action cannot be undone.
            </span>
        }
        confirmText="Delete Quote"
      />
    </div>
  );
};

export default Quotes;
