import React, { useState, useMemo } from 'react';
import { Supplier, NavigablePageProps } from '../types';
import Table from '../components/ui/Table';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import Dropdown from '../components/ui/Dropdown';
import ConfirmationModal from '../components/ui/ConfirmationModal';
import SupplierStatusSummary from '../components/suppliers/SupplierStatusSummary';
import SupplierFilterBar from '../components/suppliers/SupplierFilterBar';
import SupplierFormModal from '../components/suppliers/SupplierFormModal';
import { useQuery, useMutation } from "convex/react";
import { api } from "../convex/_generated/api";

const createId = (prefix: string) => `${prefix}-${Math.random().toString(36).substr(2, 9)}`;

const Suppliers: React.FC<NavigablePageProps> = ({ workspaceId, navigate }) => {
  // Fetch data from Convex
  const allSuppliers = useQuery(api.suppliers.getSuppliers, { workspaceId: workspaceId as any });

  // Mutations
  const createSupplier = useMutation(api.suppliers.createSupplier);
  const updateSupplier = useMutation(api.suppliers.updateSupplier);
  const deleteSupplier = useMutation(api.suppliers.deleteSupplier);

  const isLoading = allSuppliers === undefined;
  
  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(null);

  // Modal states
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isConfirmDeleteOpen, setIsConfirmDeleteOpen] = useState(false);
  const [supplierToDelete, setSupplierToDelete] = useState<Supplier | null>(null);

  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('All');

  const { filteredSuppliers, categories } = useMemo(() => {
    if (!allSuppliers) return { filteredSuppliers: [], categories: ['All'] };
    const uniqueCategories: string[] = ['All', ...new Set(allSuppliers.map(s => s.category))];
    const filtered = allSuppliers
      .filter(s => categoryFilter === 'All' || s.category === categoryFilter)
      .filter(s => {
        const term = searchTerm.toLowerCase();
        return !term || s.name.toLowerCase().includes(term) || s.email.toLowerCase().includes(term);
      });
    return { filteredSuppliers: filtered, categories: uniqueCategories };
  }, [allSuppliers, searchTerm, categoryFilter]);

  const handleSave = async (supplierData: Omit<Supplier, '_id' | '_creationTime' | 'workspaceId'>, id?: string) => {
    try {
      if (id) {
        await updateSupplier({
          supplierId: id as any,
          name: supplierData.name,
          email: supplierData.email,
          phone: supplierData.phone,
          address: supplierData.address,
        });
      } else {
        await createSupplier({
          workspaceId: workspaceId as any,
          name: supplierData.name,
          email: supplierData.email,
          phone: supplierData.phone,
          address: supplierData.address,
        });
      }
      setIsFormOpen(false);
      setSelectedSupplier(null);
    } catch (error) {
      console.error('Failed to save supplier:', error);
    }
  };
  
  const handleNew = () => {
    setSelectedSupplier(null);
    setIsFormOpen(true);
  };

  const handleEdit = (supplier: Supplier) => {
    setSelectedSupplier(supplier);
    setIsFormOpen(true);
  };
  
  const handleDeleteRequest = (supplier: Supplier) => {
    setSupplierToDelete(supplier);
    setIsConfirmDeleteOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (supplierToDelete) {
      mockData.suppliers = mockData.suppliers.filter(s => s._id !== supplierToDelete._id);
      setAllSuppliers(mockData.suppliers.filter(s => s.workspaceId === workspaceId));
      setSupplierToDelete(null);
    }
    setIsConfirmDeleteOpen(false);
  };

  const columns = [
    { key: 'name' as keyof Supplier, header: 'Supplier Name' },
    { key: 'email' as keyof Supplier, header: 'Email' },
    { key: 'category' as keyof Supplier, header: 'Category' },
    {
      key: 'actions' as 'actions',
      header: '',
      render: (item: Supplier) => (
        <div className="text-right">
          <Dropdown
            trigger={
              <Button variant="ghost" size="sm" className="w-8 h-8 p-0">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" /></svg>
              </Button>
            }
          >
            <div className="py-1">
              <button onClick={() => navigate('Bills', { filter: item.name })} className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">View Bills</button>
              <button onClick={() => navigate('Purchases', { filter: item.name })} className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">View Purchases</button>
              <div className="border-t border-gray-100 my-1"></div>
              <button onClick={() => handleEdit(item)} className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Edit</button>
              <div className="border-t border-gray-100 my-1"></div>
              <button onClick={() => handleDeleteRequest(item)} className="flex items-center w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-100">Delete</button>
            </div>
          </Dropdown>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-4">
        <h2 className="text-xl font-semibold text-gray-900">Suppliers</h2>
        <Button variant="primary" onClick={handleNew}>New Supplier</Button>
      </div>
      
      <SupplierStatusSummary suppliers={allSuppliers} isLoading={isLoading} />
      
      <SupplierFilterBar
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        categoryFilter={categoryFilter}
        setCategoryFilter={setCategoryFilter}
        categories={categories}
      />

      <Card>
        {isLoading ? (
          <div className="space-y-2 p-6">
            <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
            {[...Array(5)].map((_, i) => <div key={i} className="h-12 bg-gray-100 rounded animate-pulse"></div>)}
          </div>
        ) : (
          <Table<Supplier> columns={columns} data={filteredSuppliers.map(s => ({...s, id: s._id}))} />
        )}
      </Card>
      
      <SupplierFormModal 
        supplier={selectedSupplier}
        isOpen={isFormOpen}
        onClose={() => setIsFormOpen(false)}
        onSave={(data) => handleSave(data, selectedSupplier?._id)}
      />

      <ConfirmationModal
        isOpen={isConfirmDeleteOpen}
        onClose={() => setIsConfirmDeleteOpen(false)}
        onConfirm={handleDeleteConfirm}
        title="Delete Supplier"
        message={
            <span>
                Are you sure you want to delete <strong>{supplierToDelete?.name}</strong>? 
                This action cannot be undone.
            </span>
        }
        confirmText="Delete Supplier"
      />
    </div>
  );
};

export default Suppliers;