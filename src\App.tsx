import React, { useState, useCallback, useEffect } from 'react';
import Sidebar from '../components/layout/Sidebar';
import Header from '../components/layout/Header';
import PageWrapper from '../components/layout/PageWrapper';
import Dashboard from '../pages/Dashboard';
import { Invoices } from '../pages/Invoices';
import Bills from '../pages/Bills';
import Inventory from '../pages/Inventory';
import Reports from '../pages/Reports';
import Settings from '../pages/Settings';
import AIAssistant from '../components/ai/AIAssistant';
import { Page, User, Workspace } from '../types';
import Quotes from '../pages/Quotes';
import Transactions from '../pages/Transactions';
import ChartOfAccounts from '../pages/ChartOfAccounts';
import Team from '../pages/Team';
import Clients from '../pages/Clients';
import Purchases from '../pages/Purchases';
import Suppliers from '../pages/Suppliers';
import { mockData } from '../lib/mockData';
import LandingPage from '../pages/LandingPage';
import ContactPage from '../pages/ContactPage';
import PricingPage from '../pages/PricingPage';
import AuthPage from '../pages/AuthPage';
import { BrowserRouter as Router, Route, Routes, Navigate, useNavigate, useLocation } from 'react-router-dom';
import { useQuery } from "convex/react";
import { api } from "../convex/_generated/api";

const App: React.FC = () => {
  const [currentUserId, setCurrentUserId] = useState<string | null>(
    localStorage.getItem('userId')
  );
  const currentUser = useQuery(api.users.getCurrentUser,
    currentUserId ? { userId: currentUserId as any } : { userId: undefined }
  );



  // Listen for localStorage changes (for when user logs in)
  useEffect(() => {
    const handleStorageChange = () => {
      setCurrentUserId(localStorage.getItem('userId'));
    };

    // Listen for storage events (from other tabs)
    window.addEventListener('storage', handleStorageChange);

    // Also check periodically for changes in the same tab
    const interval = setInterval(() => {
      const storedUserId = localStorage.getItem('userId');
      if (storedUserId !== currentUserId) {
        setCurrentUserId(storedUserId);
      }
    }, 100);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      clearInterval(interval);
    };
  }, [currentUserId]);

  return (
    <Router
      future={{
        v7_startTransition: true,
        v7_relativeSplatPath: true,
      }}
    >
      <Routes>
        <Route path="/" element={<LandingPage onLogin={() => {}} />} />
        <Route path="/contact" element={<ContactPage />} />
        <Route path="/pricing" element={<PricingPage />} />
        <Route path="/auth" element={<AuthPage />} />
        <Route
          path="/dashboard/*"
          element={currentUserId ? <DashboardLayout /> : <Navigate to="/auth" />}
        />
      </Routes>
    </Router>
  );
};

const DashboardLayout: React.FC = () => {
  const routerNavigate = useNavigate();
  const location = useLocation();
  const [pageContext, setPageContext] = useState<any>(null);
  const [isAiAssistantOpen, setIsAiAssistantOpen] = useState(false);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  // Determine current page from URL
  const getCurrentPageFromPath = (pathname: string): Page => {
    const path = pathname.replace('/dashboard', '').replace('/', '');
    switch (path) {
      case 'invoices': return 'Invoices';
      case 'quotes': return 'Quotes';
      case 'clients': return 'Clients';
      case 'suppliers': return 'Suppliers';
      case 'bills': return 'Bills';
      case 'purchases': return 'Purchases';
      case 'inventory': return 'Inventory';
      case 'transactions': return 'Transactions';
      case 'reports': return 'Reports';
      case 'chart-of-accounts': return 'Chart of Accounts';
      case 'team': return 'Team';
      case 'settings': return 'Settings';
      default: return 'Dashboard';
    }
  };

  const currentPage = getCurrentPageFromPath(location.pathname);

  // Get current user from Convex
  const currentUserId = localStorage.getItem('userId');
  const currentUser = useQuery(api.users.getCurrentUser,
    currentUserId ? { userId: currentUserId as any } : { userId: undefined }
  );

  // Get workspaces for the current user
  const workspaces = useQuery(api.users.getUserWorkspaces,
    currentUser ? { userId: currentUser._id } : "skip"
  );

  const [activeWorkspaceId, setActiveWorkspaceId] = useState<string | null>(null);

  useEffect(() => {
    if (workspaces && workspaces.length > 0 && !activeWorkspaceId) {
      setActiveWorkspaceId(workspaces[0]._id);
    }
  }, [workspaces, activeWorkspaceId]);

  const handleLogout = useCallback(() => {
    // Show confirmation dialog
    const confirmed = window.confirm('Are you sure you want to log out?');

    if (confirmed) {
      // Clear user data from localStorage
      localStorage.removeItem('userId');

      // Redirect to auth page
      routerNavigate('/auth');
    }
  }, [routerNavigate]);

  // Add keyboard shortcut for logout (Ctrl+Shift+L)
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.shiftKey && event.key === 'L') {
        event.preventDefault();
        handleLogout();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [handleLogout]);

  const handleSwitchWorkspace = (workspaceId: string) => {
    const newWorkspace = workspaces?.find(w => w._id === workspaceId);
    if (newWorkspace) {
      setActiveWorkspaceId(newWorkspace._id);
    }
  };

  const navigateToPage = (page: Page, context?: any) => {
    if (context) {
      setPageContext(context);
    }

    // Convert page name to URL path
    const getPathFromPage = (pageName: Page): string => {
      switch (pageName) {
        case 'Dashboard': return '/dashboard';
        case 'Invoices': return '/dashboard/invoices';
        case 'Quotes': return '/dashboard/quotes';
        case 'Clients': return '/dashboard/clients';
        case 'Suppliers': return '/dashboard/suppliers';
        case 'Bills': return '/dashboard/bills';
        case 'Purchases': return '/dashboard/purchases';
        case 'Inventory': return '/dashboard/inventory';
        case 'Transactions': return '/dashboard/transactions';
        case 'Reports': return '/dashboard/reports';
        case 'Chart of Accounts': return '/dashboard/chart-of-accounts';
        case 'Team': return '/dashboard/team';
        case 'Settings': return '/dashboard/settings';
        default: return '/dashboard';
      }
    };

    routerNavigate(getPathFromPage(page));
    setIsAiAssistantOpen(false);
  };

  const handleOpenTransactionModalFromAI = (prompt: string) => {
    navigateToPage('Transactions', { openAiModalWithPrompt: prompt });
    setIsAiAssistantOpen(false);
  };

  const clearPageContext = useCallback(() => {
    setPageContext(null);
  }, []);



  const activeWorkspace = workspaces?.find(w => w._id === activeWorkspaceId);

  return (
    <div className="flex h-screen bg-gray-50 text-gray-900">
      <Sidebar
        currentPage={currentPage}
        onNavigate={navigateToPage}
        isOpen={isSidebarOpen}
        setIsOpen={setIsSidebarOpen}
      />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header
          user={currentUser || null}
          workspaces={workspaces || []}
          activeWorkspace={activeWorkspace || null}
          onSwitchWorkspace={handleSwitchWorkspace}
          currentPage={currentPage}
          onOpenAiAssistant={() => setIsAiAssistantOpen(true)}
          onOpenSidebar={() => setIsSidebarOpen(true)}
          onLogout={handleLogout}
        />
        <PageWrapper>
          {!workspaces ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-gray-500">Loading workspace data...</div>
            </div>
          ) : workspaces.length === 0 ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-gray-500">No workspaces found. Please contact support.</div>
            </div>
          ) : !activeWorkspaceId ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-gray-500">Loading workspace...</div>
            </div>
          ) : (
            <Routes>
              <Route path="/" element={<Dashboard workspaceId={activeWorkspaceId} />} />
              <Route path="/invoices" element={<Invoices workspaceId={activeWorkspaceId} />} />
              <Route path="/quotes" element={<Quotes workspaceId={activeWorkspaceId} />} />
              <Route path="/clients" element={<Clients workspaceId={activeWorkspaceId} navigate={navigateToPage} />} />
              <Route path="/suppliers" element={<Suppliers workspaceId={activeWorkspaceId} navigate={navigateToPage} />} />
              <Route path="/bills" element={<Bills workspaceId={activeWorkspaceId} context={pageContext} clearPageContext={clearPageContext} />} />
              <Route path="/purchases" element={<Purchases workspaceId={activeWorkspaceId} context={pageContext} clearPageContext={clearPageContext} />} />
              <Route path="/inventory" element={<Inventory workspaceId={activeWorkspaceId} />} />
              <Route path="/transactions" element={<Transactions workspaceId={activeWorkspaceId} context={pageContext} clearPageContext={clearPageContext} />} />
              <Route path="/reports" element={<Reports workspaceId={activeWorkspaceId} />} />
              <Route path="/chart-of-accounts" element={<ChartOfAccounts workspaceId={activeWorkspaceId} />} />
              <Route path="/team" element={<Team workspaceId={activeWorkspaceId} />} />
              <Route path="/settings" element={<Settings workspaceId={activeWorkspaceId} />} />
            </Routes>
          )}
        </PageWrapper>
      </div>
      {activeWorkspace && (
        <AIAssistant
          isOpen={isAiAssistantOpen}
          onClose={() => setIsAiAssistantOpen(false)}
          workspaceId={activeWorkspace._id}
          onNavigate={navigateToPage}
          onOpenTransactionModal={handleOpenTransactionModalFromAI}
        />
      )}
    </div>
  );
};

export default App;